# chunking_utils.py
import os
import tiktoken
import tempfile
import json
import re
from markdown import markdown as md_to_html
import time
import difflib

try:
    from markdown_it import MarkdownIt
    from markdown_it.tree import SyntaxTreeNode
    MARKDOWN_IT_AVAILABLE = True
except ImportError:
    MARKDOWN_IT_AVAILABLE = False
    print("Warning: markdown-it-py not available. Please install with: pip install markdown-it-py")

# --- tiktoken encoder setup ---
tiktoken_cache_dir = tempfile.gettempdir()
os.environ["TIKTOKEN_CACHE_DIR"] = tiktoken_cache_dir
encoder = tiktoken.get_encoding("cl100k_base")

def num_tokens_from_string(string: str, model_name: str = "cl100k_base") -> int:
    """Returns the number of tokens in a text string."""
    try:
        return len(encoder.encode(string))
    except Exception:
        return 0

def truncate(string: str, max_len: int) -> str:
    """Returns truncated text if the length of text exceed max_len."""
    return encoder.decode(encoder.encode(string)[:max_len])

# --- Enhanced Helper functions for AST parsing and rendering ---
def _extract_text_from_node(node):
    """从 AST 节点提取文本内容（增强版）"""
    if hasattr(node, 'content') and node.content:
        return node.content

    text_parts = []
    if hasattr(node, 'children') and node.children:
        for child in node.children:
            if child.type == "text":
                text_parts.append(child.content)
            elif child.type == "code_inline":
                text_parts.append(f"`{child.content}`")
            elif child.type == "strong":
                text_parts.append(f"**{_extract_text_from_node(child)}**")
            elif child.type == "em":
                text_parts.append(f"*{_extract_text_from_node(child)}*")
            elif child.type == "link":
                link_text = _extract_text_from_node(child)
                href = child.attrGet('href') if hasattr(child, 'attrGet') else ''
                text_parts.append(f"[{link_text}]({href or ''})")
            elif child.type == "image":
                alt_text = _extract_text_from_node(child)
                src = child.attrGet('src') if hasattr(child, 'attrGet') else ''
                text_parts.append(f"![{alt_text}]({src or ''})")
            elif child.type == "softbreak":
                text_parts.append(" ")
            elif child.type == "hardbreak":
                text_parts.append("\n")
            else:
                text_parts.append(_extract_text_from_node(child))

    return "".join(text_parts)

def _render_table_from_ast(table_node):
    """从 AST 渲染表格（增强版）"""
    try:
        table_md = []
        header_cells = []

        for child in table_node.children:
            if child.type == "thead":
                for row in child.children:
                    if row.type == "tr":
                        cells = []
                        for cell in row.children:
                            if cell.type in ["th", "td"]:
                                cell_content = _extract_text_from_node(cell).strip()
                                cells.append(cell_content)
                        if cells:
                            header_cells = cells
                            table_md.append("| " + " | ".join(cells) + " |")

                # 添加分隔符行
                if header_cells:
                    separator = "| " + " | ".join(["---"] * len(header_cells)) + " |"
                    table_md.append(separator)

            elif child.type == "tbody":
                for row in child.children:
                    if row.type == "tr":
                        cells = []
                        for cell in row.children:
                            if cell.type in ["th", "td"]:
                                cell_content = _extract_text_from_node(cell).strip()
                                cells.append(cell_content)
                        if cells:
                            # 确保行的列数与表头一致
                            while len(cells) < len(header_cells):
                                cells.append("")
                            table_md.append("| " + " | ".join(cells[:len(header_cells)]) + " |")

        if table_md:
            table_markdown = "\n".join(table_md)
            # 保持表格的markdown格式，便于RAG检索
            return table_markdown
        else:
            return _extract_text_from_node(table_node)

    except Exception as e:
        print(f"Table rendering error: {e}")
        return _extract_text_from_node(table_node)

def _render_list_from_ast(list_node):
    """从 AST 渲染列表"""
    list_items = []
    list_type = list_node.attrGet('type') or 'bullet'
    for i, item in enumerate(list_node.children):
        if item.type == "list_item":
            item_content = _extract_text_from_node(item)
            if list_type == 'ordered':
                list_items.append(f"{i+1}. {item_content}")
            else:
                list_items.append(f"- {item_content}")
    return "\n".join(list_items)

def _render_blockquote_from_ast(blockquote_node):
    """从 AST 渲染引用块"""
    content = _extract_text_from_node(blockquote_node)
    lines = content.split('\n')
    return '\n'.join(f"> {line}" for line in lines)

def _render_node_content(node):
    """渲染单个节点的内容"""
    if node.type == "table":
        return _render_table_from_ast(node)
    elif node.type == "code_block":
        return f"```{node.info or ''}\n{node.content}```"
    elif node.type == "blockquote":
        return _render_blockquote_from_ast(node)
    elif node.type in ["bullet_list", "ordered_list"]:
        return _render_list_from_ast(node)
    elif node.type == "paragraph":
        return _extract_text_from_node(node)
    elif node.type == "hr":
        return "---"
    else:
        return _extract_text_from_node(node)

# --- Basic Chunking (Fallback) ---
def _extract_tables_and_remainder_md(txt: str) -> tuple[str, list[str]]:
    """
    Extracts markdown tables from text and returns the remaining text
    and a list of table strings.
    This is a simplified implementation.
    """
    lines = txt.split('\n')
    tables = []
    remainder_lines = []
    in_table = False
    current_table = []

    for line in lines:
        stripped_line = line.strip()
        is_table_line = stripped_line.startswith('|') and stripped_line.endswith('|')
        is_separator_line = True
        if is_table_line and '-' in stripped_line:
            parts = [p.strip() for p in stripped_line[1:-1].split('|')]
            if not all(set(p) <= set('-:') for p in parts if p):
                is_separator_line = False
            if not parts:
                is_separator_line = False
        else:
            is_separator_line = False

        if is_table_line or (in_table and stripped_line):
            if not in_table and is_table_line and not is_separator_line:
                next_line_index = lines.index(line) + 1
                if next_line_index < len(lines):
                    next_line_stripped = lines[next_line_index].strip()
                    next_is_separator = next_line_stripped.startswith('|') and next_line_stripped.endswith('|') and '-' in next_line_stripped
                    if next_is_separator:
                        parts_next = [p.strip() for p in next_line_stripped[1:-1].split('|')]
                        if not all(set(p) <= set('-:') for p in parts_next if p):
                            next_is_separator = False
                        if not parts_next:
                            next_is_separator = False
                    if next_is_separator:
                        in_table = True
                        current_table.append(line)
                    else:
                        remainder_lines.append(line)
                else:
                     remainder_lines.append(line)
            elif in_table:
                current_table.append(line)
                if not is_table_line and not stripped_line:
                    tables.append("\n".join(current_table))
                    current_table = []
                    in_table = False
                    remainder_lines.append(line)
            else:
                remainder_lines.append(line)
        elif in_table and not stripped_line :
            tables.append("\n".join(current_table))
            current_table = []
            in_table = False
            remainder_lines.append(line)
        elif in_table and not is_table_line :
            tables.append("\n".join(current_table))
            current_table = []
            in_table = False
            remainder_lines.append(line)
        else:
            remainder_lines.append(line)

    if current_table:
        tables.append("\n".join(current_table))

    return "\n".join(remainder_lines), tables

def split_markdown_to_chunks(txt, chunk_token_num=128, delimiter="\n!?。；！？"):
    """
    Splits markdown text into chunks, processing tables separately and merging text sections
    to be consistent with RAGFlow's naive.py markdown handling.
    """
    if not txt or not txt.strip():
        return []

    remainder_text, extracted_tables = _extract_tables_and_remainder_md(txt)
    
    processed_chunks = []
    for table_md in extracted_tables:
        if table_md.strip():
            try:
                table_html = md_to_html(table_md, extensions=['markdown.extensions.tables'])
                processed_chunks.append(table_html)
            except Exception as e:
                processed_chunks.append(table_md)
                print(f"[WARNING] Failed to convert table to HTML: {e}. Added raw table markdown.")

    initial_sections = []
    if remainder_text and remainder_text.strip():
        for sec_line in remainder_text.split("\n"):
            line_content = sec_line.strip()
            if not line_content:
                initial_sections.append(sec_line)
                continue

            if num_tokens_from_string(sec_line) > 3 * chunk_token_num:
                mid_point = len(sec_line) // 2
                initial_sections.append(sec_line[:mid_point])
                initial_sections.append(sec_line[mid_point:])
            else:
                initial_sections.append(sec_line)
    
    final_text_chunks = []
    current_chunk_parts = []
    current_token_count = 0

    for section_text in initial_sections:
        section_token_count = num_tokens_from_string(section_text)
        
        if not section_text.strip() and not current_chunk_parts:
            continue

        if current_token_count + section_token_count <= chunk_token_num:
            current_chunk_parts.append(section_text)
            current_token_count += section_token_count
        else:
            if current_chunk_parts:
                final_text_chunks.append("\n".join(current_chunk_parts).strip())
            
            if section_token_count > chunk_token_num and section_token_count <= 3 * chunk_token_num:
                 final_text_chunks.append(section_text.strip())
                 current_chunk_parts = []
                 current_token_count = 0
            elif section_token_count > 3 * chunk_token_num:
                mid = len(section_text) // 2
                final_text_chunks.append(section_text[:mid].strip())
                final_text_chunks.append(section_text[mid:].strip())
                current_chunk_parts = []
                current_token_count = 0
            else:
                current_chunk_parts = [section_text]
                current_token_count = section_token_count
    
    if current_chunk_parts:
        final_text_chunks.append("\n".join(current_chunk_parts).strip())

    all_chunks = [chunk for chunk in processed_chunks if chunk.strip()]
    all_chunks.extend([chunk for chunk in final_text_chunks if chunk.strip()])
    
    return all_chunks

# --- Smart AST Chunking ---
def _update_context_stack(context_stack, level, title):
    """更新标题上下文栈"""
    while context_stack and context_stack[-1]['level'] >= level:
        context_stack.pop()
    context_stack.append({'level': level, 'title': title})

def _finalize_ast_chunk(chunk_parts, context_stack):
    """完成基于 AST 的 chunk 格式化"""
    chunk_content = "\n\n".join(chunk_parts).strip()
    return chunk_content

def _is_semantic_boundary(node, content):
    """判断是否为语义边界（增强版）"""
    node_type = node.type

    # 标题总是语义边界
    if node_type == "heading":
        return True

    # 水平分割线是语义边界
    if node_type == "hr":
        return True

    # 大型表格是语义边界
    if node_type == "table":
        table_tokens = num_tokens_from_string(content)
        return table_tokens > 200

    # 长代码块是语义边界
    if node_type == "code_block":
        code_tokens = num_tokens_from_string(content)
        return code_tokens > 150

    # 长列表是语义边界
    if node_type in ["bullet_list", "ordered_list"]:
        list_tokens = num_tokens_from_string(content)
        return list_tokens > 100

    # 长引用块是语义边界
    if node_type == "blockquote":
        quote_tokens = num_tokens_from_string(content)
        return quote_tokens > 100

    return False

def _process_ast_node(node, context_stack, chunk_token_num, min_chunk_tokens):
    """
    处理 AST 节点，返回 (内容, 是否应该分块)（增强版）
    """
    node_type = node.type
    content = ""

    if node_type == "heading":
        level = int(node.tag[1])
        title_text = _extract_text_from_node(node)
        _update_context_stack(context_stack, level, title_text)
        content = node.markup + " " + title_text
    elif node_type == "table":
        content = _render_table_from_ast(node)
    elif node_type == "code_block":
        # 保持代码块的完整性和格式
        lang = node.info or ''
        code_content = node.content.rstrip('\n')
        content = f"```{lang}\n{code_content}\n```"
    elif node_type == "blockquote":
        content = _render_blockquote_from_ast(node)
    elif node_type in ["bullet_list", "ordered_list"]:
        content = _render_list_from_ast(node)
    elif node_type == "paragraph":
        content = _extract_text_from_node(node)
    elif node_type == "hr":
        content = "---"
    elif node_type == "fence":
        # 处理围栏代码块
        content = f"```{node.info or ''}\n{node.content}```"
    else:
        content = _extract_text_from_node(node)

    # 判断是否应该分块
    should_break = _is_semantic_boundary(node, content)

    return content, should_break

def _should_merge_with_previous(current_content, previous_content, chunk_token_num):
    """判断是否应该与前一个分块合并"""
    current_tokens = num_tokens_from_string(current_content)
    previous_tokens = num_tokens_from_string(previous_content)

    # 如果当前分块很小，尝试合并
    if current_tokens < chunk_token_num * 0.3:
        merged_tokens = current_tokens + previous_tokens
        if merged_tokens <= chunk_token_num * 1.2:
            return True

    return False

def _add_context_to_chunk(chunk_content, context_stack):
    """为分块添加上下文信息"""
    if not context_stack:
        return chunk_content

    # 检查分块是否已经包含标题
    lines = chunk_content.split('\n')
    has_heading = any(line.strip().startswith('#') for line in lines[:3])

    if not has_heading:
        # 添加最相关的上下文标题
        context_title = context_stack[-1]['title'] if context_stack else None
        context_level = context_stack[-1]['level'] if context_stack else 3

        if context_title and len(context_title) > 3:
            context_header = f"{'#' * min(context_level, 3)} {context_title}"
            return f"{context_header}\n\n{chunk_content}"

    return chunk_content

def split_markdown_to_chunks_smart(txt, chunk_token_num=256, min_chunk_tokens=10):
    """
    基于 markdown-it-py AST 的智能分块方法（增强版），解决 RAG Markdown 文件分块问题：
    1. 基于语义切分（使用 AST）
    2. 维护表格、代码块完整性
    3. 智能语义边界检测
    4. 上下文感知的分块合并
    5. 自动添加上下文标题
    """
    if not MARKDOWN_IT_AVAILABLE:
        print("Warning: markdown-it-py not available, falling back to simple chunking")
        return split_markdown_to_chunks(txt, chunk_token_num)

    if not txt or not txt.strip():
        return []

    md = MarkdownIt("commonmark", {"breaks": True, "html": True})
    md.enable(['table', 'strikethrough', 'linkify'])

    try:
        tokens = md.parse(txt)
        tree = SyntaxTreeNode(tokens)

        chunks = []
        current_chunk = []
        current_tokens = 0
        context_stack = []

        for node in tree.children:
            chunk_data, should_break = _process_ast_node(
                node, context_stack, chunk_token_num, min_chunk_tokens
            )

            if not chunk_data or not chunk_data.strip():
                continue

            chunk_tokens = num_tokens_from_string(chunk_data)

            # 检查是否需要分块
            needs_split = (
                should_break or
                (current_tokens + chunk_tokens > chunk_token_num and
                 current_chunk and current_tokens >= min_chunk_tokens)
            )

            if needs_split and current_chunk:
                chunk_content = _finalize_ast_chunk(current_chunk, context_stack)
                chunk_content = _add_context_to_chunk(chunk_content, context_stack)

                if chunk_content.strip():
                    chunks.append(chunk_content)

                current_chunk = []
                current_tokens = 0

            current_chunk.append(chunk_data)
            current_tokens += chunk_tokens

        # 处理最后一个分块
        if current_chunk:
            chunk_content = _finalize_ast_chunk(current_chunk, context_stack)
            chunk_content = _add_context_to_chunk(chunk_content, context_stack)

            if chunk_content.strip():
                # 尝试与前一个分块合并（如果太小）
                if (chunks and
                    _should_merge_with_previous(chunk_content, chunks[-1], chunk_token_num)):
                    chunks[-1] = chunks[-1] + "\n\n" + chunk_content
                else:
                    chunks.append(chunk_content)

        # 过滤空分块并进行最终优化
        final_chunks = []
        for chunk in chunks:
            if chunk.strip():
                # 移除多余的空行
                cleaned_chunk = re.sub(r'\n{3,}', '\n\n', chunk.strip())
                final_chunks.append(cleaned_chunk)

        return final_chunks

    except Exception as e:
        print(f"AST parsing failed: {e}, falling back to simple chunking")
        return split_markdown_to_chunks(txt, chunk_token_num)

# --- Advanced Enhanced Chunking ---
def _extract_nodes_with_header_info(tree, headers_to_split_on):
    """提取所有节点及其对应的标题信息"""
    nodes_with_headers = []
    current_headers = {}
    
    for node in tree.children:
        if node.type == "heading":
            level = int(node.tag[1])
            title = _extract_text_from_node(node)
            
            current_headers = {k: v for k, v in current_headers.items() if k < level}
            current_headers[level] = title
            
            is_split_boundary = level in headers_to_split_on
            
            nodes_with_headers.append({
                'node': node,
                'type': 'heading',
                'level': level,
                'title': title,
                'headers': current_headers.copy(),
                'is_split_boundary': is_split_boundary,
                'content': node.markup + " " + title
            })
        else:
            content = _render_node_content(node)
            if content.strip():
                nodes_with_headers.append({
                    'node': node,
                    'type': node.type,
                    'headers': current_headers.copy(),
                    'is_split_boundary': False,
                    'content': content
                })
    return nodes_with_headers

def _split_by_header_levels(nodes_with_headers, headers_to_split_on):
    """基于标题层级进行分块，智能处理连续标题"""
    chunks = []
    current_chunk = {
        'headers': {},
        'nodes': []
    }
    
    i = 0
    while i < len(nodes_with_headers):
        node_info = nodes_with_headers[i]
        
        if node_info['is_split_boundary']:
            if node_info['type'] == 'heading':
                current_title = node_info.get('title', '').strip()
                
                is_short_title = (
                    len(current_title) <= 12 and 
                    (
                        (current_title.replace('.', '').replace(' ', '').isdigit()) or
                        (len(current_title.split()) <= 2 and 
                         any(char.isdigit() for char in current_title))
                    )
                )
                
                if is_short_title:
                    found_content_header = False
                    j = i + 1
                    while j < len(nodes_with_headers) and j < i + 4:
                        next_node = nodes_with_headers[j]
                        if next_node.get('type') == 'heading':
                            next_title = next_node.get('title', '').strip()
                            is_content_header = (
                                len(next_title) > 12 or
                                (len(next_title.split()) > 2) or
                                any(word for word in next_title.split()
                                    if len(word) > 3 and not word.replace('.', '').isdigit())
                            )
                            if is_content_header:
                                found_content_header = True
                                break
                        elif next_node.get('content', '').strip():
                            break
                        j += 1
                    
                    if found_content_header:
                        current_chunk['nodes'].append(node_info)
                        i += 1
                        continue
            
            if (current_chunk['nodes'] and 
                any(n for n in current_chunk['nodes'] if n['content'].strip())):
                chunks.append(current_chunk)
                current_chunk = {
                    'headers': {},
                    'nodes': []
                }
        
        if node_info['headers']:
            current_chunk['headers'] = node_info['headers'].copy()
        
        current_chunk['nodes'].append(node_info)
        i += 1
    
    if current_chunk['nodes'] and any(n for n in current_chunk['nodes'] if n['content'].strip()):
        chunks.append(current_chunk)
    
    return chunks

def _render_header_chunk(chunk_info):
    """渲染基于标题的分块内容（原始版本，用于兼容性）"""
    content_parts = []
    chunk_has_header = any(node['type'] == 'heading' for node in chunk_info.get('nodes', []))
    if not chunk_has_header and chunk_info.get('headers'):
        context_header = _get_most_relevant_header(chunk_info['headers'])
        if context_header:
            content_parts.append(context_header)
    for node_info in chunk_info.get('nodes', []):
        if node_info.get('content', '').strip():
            content_parts.append(node_info['content'])
    return "\n\n".join(content_parts).strip()

def _get_most_relevant_header(headers):
    """获取最相关的上下文标题（原始版本）"""
    if not headers:
        return None
    max_level = max(headers.keys())
    return f"{'#' * max_level} {headers[max_level]}"

def _has_special_content(chunk):
    """检查分块是否包含特殊内容（表格、代码块、公式等）"""
    for node_info in chunk.get('nodes', []):
        node_type = node_info.get('type', '')
        content = node_info.get('content', '')
        if node_type in ['table', 'code_block']:
            return True
        if '$$' in content or '$' in content:
            return True
        if '<table>' in content and '</table>' in content:
            return True
    return False

def _split_oversized_chunk(chunk, target_tokens, max_tokens):
    """分割超大分块，在段落边界进行分割"""
    split_chunks = []
    nodes = chunk.get('nodes', [])
    headers = chunk.get('headers', {})
    
    current_nodes = []
    current_tokens = 0
    
    for node_info in nodes:
        node_content = node_info.get('content', '')
        node_tokens = num_tokens_from_string(node_content)
        is_heading = node_info.get('type') == 'heading'
        
        if current_tokens + node_tokens > target_tokens and current_nodes:
            new_chunk = {
                'headers': headers.copy(),
                'nodes': current_nodes.copy(),
                'chunk_type': 'split_from_oversized',
                'has_special_content': any(_has_special_content({'nodes': [n]}) for n in current_nodes)
            }
            split_chunks.append(new_chunk)
            
            current_nodes = [node_info]
            current_tokens = node_tokens
            
            if is_heading:
                level = node_info.get('level', 3)
                title = node_info.get('title', '')
                new_headers = {k: v for k, v in headers.items() if k < level}
                new_headers[level] = title
                headers = new_headers
        else:
            current_nodes.append(node_info)
            current_tokens += node_tokens
            
            if is_heading:
                level = node_info.get('level', 3)
                title = node_info.get('title', '')
                headers = {k: v for k, v in headers.items() if k < level}
                headers[level] = title
    
    if current_nodes:
        final_chunk = {
            'headers': headers.copy(),
            'nodes': current_nodes,
            'chunk_type': 'split_from_oversized',
            'has_special_content': any(_has_special_content({'nodes': [n]}) for n in current_nodes)
        }
        split_chunks.append(final_chunk)
    
    return split_chunks

def _try_merge_with_next(current_chunk, all_chunks, current_index, target_tokens):
    """尝试将小分块与后续分块合并"""
    if current_index >= len(all_chunks) - 1:
        return None
    
    next_chunk = all_chunks[current_index + 1]
    
    current_content = _render_header_chunk(current_chunk)
    next_content = _render_header_chunk(next_chunk)
    merged_tokens = num_tokens_from_string(current_content + "\n\n" + next_content)
    
    if merged_tokens <= target_tokens * 1.2:
        merged_chunk = {
            'headers': next_chunk.get('headers', current_chunk.get('headers', {})),
            'nodes': current_chunk.get('nodes', []) + next_chunk.get('nodes', []),
            'chunk_type': 'merged_small',
            'has_special_content': (_has_special_content(current_chunk) or 
                                  _has_special_content(next_chunk)),
            'merged_count': 2,
            'source_sections': 2
        }
        return merged_chunk
    
    return None

def _enhance_small_chunk_with_context(chunk):
    """为小分块增强上下文信息"""
    enhanced_chunk = chunk.copy()
    enhanced_chunk['chunk_type'] = 'small_enhanced'
    enhanced_chunk['has_special_content'] = _has_special_content(chunk)
    
    headers = chunk.get('headers', {})
    if headers:
        context_parts = []
        for level in sorted(headers.keys()):
            context_parts.append(f"{'#' * level} {headers[level]}")
        
        if context_parts:
            context_node = {
                'type': 'context',
                'content': '\n'.join(context_parts),
                'headers': headers.copy(),
                'is_split_boundary': False
            }
            enhanced_chunk['nodes'] = [context_node] + enhanced_chunk.get('nodes', [])
    
    return enhanced_chunk

def _render_header_chunk_advanced(chunk_info):
    """高级渲染基于标题的分块内容，包含更好的格式化"""
    content_parts = []
    chunk_has_header = any(node['type'] == 'heading' for node in chunk_info.get('nodes', []))
    headers = chunk_info.get('headers', {})
    
    chunk_type = chunk_info.get('chunk_type', 'normal')
    if chunk_type in ['split_from_oversized', 'small_enhanced'] and headers and not chunk_has_header:
        context_header = _get_most_relevant_header_advanced(headers, chunk_type)
        if context_header:
            content_parts.append(context_header)
    
    for node_info in chunk_info.get('nodes', []):
        if node_info.get('content', '').strip():
            content = node_info['content']
            content_parts.append(content)
    
    result = "\n\n".join(content_parts).strip()
    return result

def _get_most_relevant_header_advanced(headers, chunk_type):
    """获取最相关的上下文标题（高级版本）"""
    if not headers:
        return None
    max_level = max(headers.keys())
    return f"{'#' * max_level} {headers[max_level]}"

def _apply_size_control_and_optimization(chunks, min_tokens, target_tokens, max_tokens):
    """应用动态大小控制和优化策略"""
    optimized_chunks = []
    
    i = 0
    while i < len(chunks):
        chunk = chunks[i]
        chunk_content = _render_header_chunk(chunk)
        chunk_tokens = num_tokens_from_string(chunk_content)
        
        has_special_content = _has_special_content(chunk)
        
        if chunk_tokens <= max_tokens and chunk_tokens >= min_tokens:
            chunk['chunk_type'] = 'normal'
            chunk['has_special_content'] = has_special_content
            optimized_chunks.append(chunk)
            
        elif chunk_tokens > max_tokens and not has_special_content:
            split_chunks = _split_oversized_chunk(chunk, target_tokens, max_tokens)
            optimized_chunks.extend(split_chunks)
            
        elif chunk_tokens < min_tokens:
            merged_chunk = _try_merge_with_next(chunk, chunks, i, target_tokens)
            if merged_chunk:
                optimized_chunks.append(merged_chunk)
                i += merged_chunk.get('merged_count', 1) - 1
            else:
                enhanced_chunk = _enhance_small_chunk_with_context(chunk)
                optimized_chunks.append(enhanced_chunk)
        else:
            chunk['chunk_type'] = 'oversized_special'
            chunk['has_special_content'] = has_special_content
            optimized_chunks.append(chunk)
        
        i += 1
    return optimized_chunks

def _apply_overlap_strategy(chunks, overlap_ratio):
    """应用重叠策略，为分块添加上下文重叠"""
    if overlap_ratio <= 0 or len(chunks) <= 1:
        return chunks

    overlapped_chunks = []

    for i, chunk in enumerate(chunks):
        chunk_content = chunk if isinstance(chunk, str) else chunk.get('content', '')

        # 添加前一个分块的尾部作为上下文
        if i > 0 and overlap_ratio > 0:
            prev_chunk = chunks[i-1]
            prev_content = prev_chunk if isinstance(prev_chunk, str) else prev_chunk.get('content', '')

            # 计算重叠长度
            prev_tokens = num_tokens_from_string(prev_content)
            overlap_tokens = int(prev_tokens * overlap_ratio)

            if overlap_tokens > 10:  # 只有足够长的重叠才有意义
                prev_lines = prev_content.split('\n')
                overlap_lines = prev_lines[-max(1, len(prev_lines) // 4):]
                overlap_text = '\n'.join(overlap_lines)

                if num_tokens_from_string(overlap_text) <= overlap_tokens:
                    chunk_content = f"[上下文] {overlap_text}\n\n{chunk_content}"

        if isinstance(chunk, str):
            overlapped_chunks.append(chunk_content)
        else:
            chunk_copy = chunk.copy()
            chunk_copy['content'] = chunk_content
            overlapped_chunks.append(chunk_copy)

    return overlapped_chunks

def split_markdown_to_chunks_advanced(txt, chunk_token_num=256, min_chunk_tokens=10,
                                     overlap_ratio=0.0, include_metadata=False):
    """
    基于标题层级的高级 Markdown 分块方法（增强版）

    核心特性：
    1. 智能标题层级分析和分块边界检测
    2. 动态大小控制：自适应目标大小调整
    3. 语义完整性保护：保持表格、代码块、公式完整性
    4. 上下文感知合并：智能处理小分块
    5. 重叠策略：可选的上下文重叠增强
    6. 元数据丰富：提供详细的分块信息
    """
    if not MARKDOWN_IT_AVAILABLE:
        print("Warning: markdown-it-py not available, falling back to smart chunking")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)

    if not txt or not txt.strip():
        return []

    # 动态调整目标参数
    target_min_tokens = max(30, min_chunk_tokens)
    target_tokens = chunk_token_num
    target_max_tokens = int(chunk_token_num * 1.8)

    # 根据文档长度调整分割策略
    doc_tokens = num_tokens_from_string(txt)
    if doc_tokens > 5000:
        headers_to_split_on = [1, 2]  # 长文档使用更少的分割层级
    elif doc_tokens > 2000:
        headers_to_split_on = [1, 2, 3]
    else:
        headers_to_split_on = [1, 2, 3, 4]  # 短文档可以使用更多层级

    md = MarkdownIt("commonmark", {"breaks": True, "html": True})
    md.enable(['table', 'strikethrough', 'linkify'])

    try:
        tokens = md.parse(txt)
        tree = SyntaxTreeNode(tokens)

        nodes_with_headers = _extract_nodes_with_header_info(tree, headers_to_split_on)

        initial_chunks = _split_by_header_levels(nodes_with_headers, headers_to_split_on)

        optimized_chunks = _apply_size_control_and_optimization(
            initial_chunks, target_min_tokens, target_tokens, target_max_tokens
        )

        final_chunks = []
        for chunk_info in optimized_chunks:
            content = _render_header_chunk_advanced(chunk_info)
            if content.strip():
                if include_metadata:
                    chunk_data = {
                        'content': content,
                        'metadata': {
                            'headers': chunk_info.get('headers', {}),
                            'token_count': num_tokens_from_string(content),
                            'chunk_type': chunk_info.get('chunk_type', 'header_based'),
                            'has_special_content': chunk_info.get('has_special_content', False),
                            'source_sections': chunk_info.get('source_sections', 1),
                            'semantic_level': len(chunk_info.get('headers', {}))
                        }
                    }
                    final_chunks.append(chunk_data)
                else:
                    final_chunks.append(content)

        # 应用重叠策略
        if overlap_ratio > 0:
            final_chunks = _apply_overlap_strategy(final_chunks, overlap_ratio)

        return final_chunks

    except Exception as e:
        print(f"Advanced header-based parsing failed: {e}, falling back to smart chunking")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)

# --- Strict Regex Chunking ---
def split_markdown_to_chunks_strict_regex(txt, chunk_token_num=256, min_chunk_tokens=10, regex_pattern=''):
    """
    使用自定义正则表达式进行严格分块
    
    Args:
        txt: 要分块的文本
        chunk_token_num: 目标分块大小（tokens）
        min_chunk_tokens: 最小分块大小（tokens）
        regex_pattern: 自定义正则表达式
        
    Returns:
        分块列表
    """
    if not txt or not txt.strip():
        return []
    
    if not regex_pattern or not regex_pattern.strip():
        print(f"⚠️ [WARNING] 正则表达式为空，回退到智能分块")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)
    
    try:
        print(f"🎯 [DEBUG] 使用自定义正则表达式进行分块: {regex_pattern}")
        
        precise_pattern = r'^\s*' + regex_pattern
        
        lines = txt.split('\n')
        chunks = []
        current_chunk = []
        
        for line in lines:
            if re.search(precise_pattern, line) and current_chunk:
                chunk_content = '\n'.join(current_chunk).strip()
                if chunk_content:
                    chunks.append(chunk_content)
                
                current_chunk = [line]
            else:
                current_chunk.append(line)
        
        if current_chunk:
            chunk_content = '\n'.join(current_chunk).strip()
            if chunk_content:
                chunks.append(chunk_content)
        
        final_chunks = [chunk for chunk in chunks if chunk.strip()]
        
        print(f"📊 [DEBUG] 正则分块结果: {len(final_chunks)} 个分块")
        if final_chunks:
            token_counts = [num_tokens_from_string(chunk) for chunk in final_chunks]
            print(f"📈 [DEBUG] Token分布: {min(token_counts)}-{max(token_counts)} (平均: {sum(token_counts)/len(token_counts):.1f})")
        
        return final_chunks
        
    except re.error as e:
        print(f"❌ [ERROR] 自定义正则分块失败，正则表达式错误: {e}，回退到智能分块")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)
    except Exception as e:
        print(f"❌ [ERROR] 自定义正则分块发生异常: {e}，回退到智能分块")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)

# --- Enhanced Main Dispatcher (KnowFlow-inspired) ---
def split_markdown_to_chunks_configured(txt, strategy='smart', chunk_token_num=256, min_chunk_tokens=10, **kwargs):
    """
    根据配置选择合适的分块方法的统一接口（KnowFlow增强版）

    支持的分块方法：
    - 'strict_regex': 严格按正则表达式分块
    - 'advanced': 高级分块，混合策略，动态阈值调整
    - 'smart': 智能分块，基于AST，语义感知（默认推荐）
    - 'basic': 基础分块，简单快速

    Args:
        txt: 待分块的文本
        strategy: 分块策略
        chunk_token_num: 目标分块大小（tokens）
        min_chunk_tokens: 最小分块大小（tokens）
        **kwargs: 其他参数，包括 chunking_config, regex_pattern, overlap_ratio, include_metadata
    """
    print("=" * 80)
    print("🔍 [DEBUG] split_markdown_to_chunks_configured 调用参数:")
    print(f"📝 文本长度: {len(txt) if txt else 0} 字符")
    print(f"📋 策略: {strategy}")
    print(f"🔢 chunk_token_num: {chunk_token_num}")
    print(f"🔢 min_chunk_tokens: {min_chunk_tokens}")
    print(f"📋 kwargs 键值对:")
    for key, value in kwargs.items():
        if key == 'chunking_config' and isinstance(value, dict):
            print(f"  📌 {key}:")
            for sub_key, sub_value in value.items():
                print(f"    🔸 {sub_key}: {sub_value}")
        else:
            print(f"  📌 {key}: {value}")
    print("=" * 80)

    # 检查是否有自定义的分块配置（从文档配置传入）
    custom_chunking_config = kwargs.get('chunking_config', None)
    if custom_chunking_config:
        print(f"🎯 [DEBUG] 使用自定义分块配置: {custom_chunking_config}")
        # 使用文档级别的分块配置
        strategy = custom_chunking_config.get('strategy', strategy)
        chunk_token_num = custom_chunking_config.get('chunk_token_num', chunk_token_num)
        min_chunk_tokens = custom_chunking_config.get('min_chunk_tokens', min_chunk_tokens)

        print(f"🚀 [DEBUG] 最终分块参数:")
        print(f"  📋 策略: {strategy}")
        print(f"  🔢 分块大小: {chunk_token_num}")
        print(f"  🔢 最小分块: {min_chunk_tokens}")

    # 策略分发处理
    if strategy == 'advanced':
        include_metadata = kwargs.get('include_metadata', custom_chunking_config.get('include_metadata', False) if custom_chunking_config else False)
        overlap_ratio = kwargs.get('overlap_ratio', custom_chunking_config.get('overlap_ratio', 0.0) if custom_chunking_config else 0.0)
        print(f"  🎯 使用高级分块策略")
        return split_markdown_to_chunks_advanced(
            txt,
            chunk_token_num=chunk_token_num,
            min_chunk_tokens=min_chunk_tokens,
            overlap_ratio=overlap_ratio,
            include_metadata=include_metadata
        )
    elif strategy == 'strict_regex':
        regex_pattern = kwargs.get('regex_pattern', custom_chunking_config.get('regex_pattern', '') if custom_chunking_config else '')
        print(f"  🎯 使用正则分块策略, 模式: {regex_pattern}")
        if regex_pattern:
            return split_markdown_to_chunks_strict_regex(
                txt,
                chunk_token_num=chunk_token_num,
                min_chunk_tokens=min_chunk_tokens,
                regex_pattern=regex_pattern
            )
        else:
            print(f"  ⚠️ 正则表达式为空，回退到智能分块")
            return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)
    elif strategy == 'smart':
        print(f"  🎯 使用智能分块策略")
        return split_markdown_to_chunks_smart(
            txt,
            chunk_token_num=chunk_token_num,
            min_chunk_tokens=min_chunk_tokens
        )
    elif strategy == 'basic':
        delimiter = kwargs.get('delimiter', custom_chunking_config.get('delimiter', "\n!?。；！？") if custom_chunking_config else "\n!?。；！？")
        print(f"  🎯 使用基础分块策略, 分隔符: {delimiter}")
        return split_markdown_to_chunks(
            txt,
            chunk_token_num=chunk_token_num,
            delimiter=delimiter
        )
    else:
        print(f"⚠️ 未知分块策略 '{strategy}'，回退到智能分块")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)

def split_markdown_to_chunks_strict_regex(txt, chunk_token_num=256, min_chunk_tokens=10, regex_pattern=''):
    """
    使用自定义正则表达式进行严格分块（KnowFlow增强版）

    Args:
        txt: 要分块的文本
        chunk_token_num: 目标分块大小（tokens）
        min_chunk_tokens: 最小分块大小（tokens）
        regex_pattern: 自定义正则表达式

    Returns:
        分块列表
    """
    if not txt or not txt.strip():
        return []

    if not regex_pattern or not regex_pattern.strip():
        print(f"⚠️ [WARNING] 正则表达式为空，回退到智能分块")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)

    try:
        print(f"🎯 [DEBUG] 使用自定义正则表达式进行分块: {regex_pattern}")

        # 使用更精确的方法：逐行处理，确保每个匹配都开始新分块
        # 优化正则表达式，只匹配行开头或前面只有空格的条文
        precise_pattern = r'^\s*' + regex_pattern

        lines = txt.split('\n')
        chunks = []
        current_chunk = []

        for line in lines:
            # 检查当前行是否以正则表达式匹配开始（真正的条文开始）
            if re.search(precise_pattern, line) and current_chunk:
                # 如果当前行包含匹配且当前已有内容，先保存当前分块
                chunk_content = '\n'.join(current_chunk).strip()
                if chunk_content:
                    chunks.append(chunk_content)
                # 开始新分块
                current_chunk = [line]
            else:
                # 将当前行添加到当前分块
                current_chunk.append(line)

        # 添加最后一个分块
        if current_chunk:
            chunk_content = '\n'.join(current_chunk).strip()
            if chunk_content:
                chunks.append(chunk_content)

        # 过滤和统计
        final_chunks = [chunk for chunk in chunks if chunk.strip()]
        print(f"📊 [DEBUG] 正则分块结果: {len(final_chunks)} 个分块")

        if final_chunks:
            token_counts = [num_tokens_from_string(chunk) for chunk in final_chunks]
            print(f"📈 [DEBUG] Token分布: {min(token_counts)}-{max(token_counts)} (平均: {sum(token_counts)/len(token_counts):.1f})")

        return final_chunks

    except re.error as e:
        print(f"❌ [ERROR] 自定义正则分块失败，正则表达式错误: {e}，回退到智能分块")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)
    except Exception as e:
        print(f"❌ [ERROR] 自定义正则分块发生异常: {e}，回退到智能分块")
        return split_markdown_to_chunks_smart(txt, chunk_token_num, min_chunk_tokens)

# --- Functions for position info (included for completeness, but not directly used in upload) ---
def longest_common_substring_length(str1, str2):
    """计算两个字符串的最长公共子串长度"""
    if not str1 or not str2:
        return 0
    m, n = len(str1), len(str2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    max_length = 0
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if str1[i-1] == str2[j-1]:
                dp[i][j] = dp[i-1][j-1] + 1
                max_length = max(max_length, dp[i][j])
            else:
                dp[i][j] = 0
    return max_length

# Placeholder for get_blocks_from_md as it requires KnowFlow's internal JSON structure
# For a standalone project, you'd need to implement how to get these 'blocks'
# from your pre-processed documents, or remove functions that rely on it if not needed.
_blocks_cache = {}
def get_blocks_from_md(md_file_path):
    print(f"WARNING: get_blocks_from_md is a placeholder. It requires KnowFlow's internal JSON structure ({md_file_path.replace('.md', '_middle.json')}).")
    print("Please ensure this JSON file exists and has the correct format if you intend to use functions relying on it (e.g., get_bbox_for_chunk).")
    # For demonstration, return empty list
    return []

def get_bbox_for_chunk(md_file_path, chunk_content):
    """
    根据 md 文件路径和 chunk 内容，返回构成该 chunk 的连续 block 的 bbox 列表。
    该算法采用混合评分机制寻找最佳"锚点" block，该评分兼顾了最长公共子串的长度和其占 block 自身长度的比例。
    然后从该锚点向前后扩展，寻找同样存在于 chunk 中的连续 block。
    这旨在平衡精确匹配和部分（截断）匹配的场景。
    
    支持Pipeline模式和VLM模式的数据结构。
    """
    print("WARNING: get_bbox_for_chunk is a placeholder. It relies on get_blocks_from_md which needs KnowFlow's internal JSON.")
    return None
