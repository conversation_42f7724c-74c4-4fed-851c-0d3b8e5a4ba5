#!/usr/bin/env python3
"""
RAGFlow分块上传工具快速开始指南

这个脚本帮助您快速配置和使用RAGFlow分块上传工具
"""

import os
import json
from ragflow_chunker import RAGFlowChunker

def setup_config():
    """设置配置"""
    print("🔧 RAGFlow分块上传工具配置")
    print("=" * 50)
    
    # 获取用户输入
    api_key = input("请输入您的RAGFlow API密钥: ").strip()
    base_url = input("请输入RAGFlow服务器地址 (默认: http://localhost:9380): ").strip()
    if not base_url:
        base_url = "http://localhost:9380"
    
    dataset_id = input("请输入数据集ID: ").strip()
    document_id = input("请输入文档ID: ").strip()
    
    config = {
        "api_key": api_key,
        "base_url": base_url,
        "dataset_id": dataset_id,
        "document_id": document_id
    }
    
    # 保存配置
    with open("user_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ 配置已保存到 user_config.json")
    return config

def load_config():
    """加载配置"""
    try:
        with open("user_config.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print("⚠️ 未找到配置文件，请先运行配置")
        return setup_config()

def quick_upload():
    """快速上传"""
    print("🚀 快速上传文档")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    
    # 选择文件
    file_path = input("请输入要上传的文档路径: ").strip()
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    # 选择分块策略
    print("\n📋 选择分块策略:")
    print("1. Smart (智能分块，推荐)")
    print("2. Advanced (高级分块，适合复杂文档)")
    print("3. Basic (基础分块，适合简单文档)")
    
    strategy_choice = input("请选择策略 (1-3, 默认1): ").strip()
    strategy_map = {
        "1": "smart",
        "2": "advanced", 
        "3": "basic",
        "": "smart"
    }
    strategy = strategy_map.get(strategy_choice, "smart")
    
    # 设置分块参数
    chunk_tokens = input("目标分块大小(tokens, 默认256): ").strip()
    chunk_tokens = int(chunk_tokens) if chunk_tokens.isdigit() else 256
    
    print(f"\n📝 开始处理...")
    print(f"📄 文件: {file_path}")
    print(f"🎯 策略: {strategy}")
    print(f"🔢 分块大小: {chunk_tokens} tokens")
    
    try:
        # 创建分块器
        chunker = RAGFlowChunker(
            api_key=config["api_key"],
            base_url=config["base_url"]
        )
        
        # 处理文件
        result = chunker.process_file(
            file_path=file_path,
            dataset_id=config["dataset_id"],
            document_id=config["document_id"],
            strategy=strategy,
            chunk_token_num=chunk_tokens
        )
        
        if result["success"]:
            print("\n🎉 上传完成!")
            print(f"📊 总分块数: {result['total_chunks']}")
            print(f"✅ 成功上传: {result['uploaded_chunks']}")
            print(f"❌ 失败数量: {result['failed_chunks']}")
        else:
            print(f"\n❌ 上传失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")

def test_connection():
    """测试连接"""
    print("🔍 测试RAGFlow连接")
    print("=" * 50)
    
    config = load_config()
    
    try:
        chunker = RAGFlowChunker(
            api_key=config["api_key"],
            base_url=config["base_url"]
        )
        
        # 尝试读取一个测试文件进行分块（不上传）
        if os.path.exists("test_document.md"):
            content = chunker.read_file("test_document.md")
            chunks = chunker.chunk_document(content, strategy="smart")
            print(f"✅ 分块功能正常: 生成了 {len(chunks)} 个分块")
        else:
            print("⚠️ 测试文档不存在，请先运行 python test_chunker.py")
        
        print("✅ 本地功能测试通过")
        print("💡 提示: 实际上传需要确保RAGFlow服务正常运行且API密钥有效")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_examples():
    """显示使用示例"""
    print("📚 使用示例")
    print("=" * 50)
    
    print("1. 命令行使用:")
    print("python ragflow_chunker.py \\")
    print("  --file document.md \\")
    print("  --dataset-id your_dataset_id \\")
    print("  --document-id your_document_id \\")
    print("  --api-key your_api_key \\")
    print("  --strategy smart")
    
    print("\n2. Python代码使用:")
    print("""
from ragflow_chunker import RAGFlowChunker

chunker = RAGFlowChunker(
    api_key="your_api_key",
    base_url="http://localhost:9380"
)

result = chunker.process_file(
    file_path="document.md",
    dataset_id="dataset_id",
    document_id="document_id",
    strategy="smart"
)

print(f"上传完成: {result['uploaded_chunks']}/{result['total_chunks']}")
""")
    
    print("\n3. 批量处理:")
    print("python example_usage.py  # 查看更多示例")

def main_menu():
    """主菜单"""
    while True:
        print("\n" + "=" * 60)
        print("🎯 RAGFlow分块上传工具 - 快速开始")
        print("=" * 60)
        print("1. 配置设置")
        print("2. 快速上传文档")
        print("3. 测试连接")
        print("4. 查看使用示例")
        print("5. 运行完整测试")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-5): ").strip()
        
        if choice == "1":
            setup_config()
        elif choice == "2":
            quick_upload()
        elif choice == "3":
            test_connection()
        elif choice == "4":
            show_examples()
        elif choice == "5":
            print("🧪 运行完整测试...")
            os.system("python test_chunker.py")
        elif choice == "0":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见!")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
