# 测试文档

## 第一章 介绍

这是一个测试文档，用于验证分块功能。

### 1.1 背景

在自然语言处理和信息检索领域，文档分块是一个重要的预处理步骤。

### 1.2 目标

我们的目标是：
- 保持语义完整性
- 控制分块大小
- 处理特殊结构

## 第二章 技术细节

### 2.1 算法原理

分块算法基于以下原理：

1. **语义边界识别**: 识别自然的语义边界
2. **大小控制**: 控制每个分块的token数量
3. **结构保持**: 保持markdown结构的完整性

### 2.2 实现方法

#### 2.2.1 AST解析

使用markdown-it-py进行AST解析：

```python
from markdown_it import MarkdownIt
md = MarkdownIt()
tokens = md.parse(text)
```

#### 2.2.2 分块策略

支持多种分块策略：

| 策略 | 描述 | 适用场景 |
|------|------|----------|
| Smart | 智能分块 | 一般文档 |
| Advanced | 高级分块 | 复杂文档 |
| Basic | 基础分块 | 简单文档 |

### 2.3 参数配置

重要参数包括：
- `chunk_token_num`: 目标分块大小
- `min_chunk_tokens`: 最小分块大小
- `overlap_ratio`: 重叠比例

## 第三章 使用示例

### 3.1 基本使用

```python
from ragflow_chunker import RAGFlowChunker

chunker = RAGFlowChunker(api_key="key", base_url="url")
result = chunker.process_file("doc.md", "dataset_id", "doc_id")
```

### 3.2 高级配置

可以通过参数进行高级配置：

```python
result = chunker.process_file(
    file_path="document.md",
    dataset_id="dataset_id",
    document_id="document_id",
    strategy="advanced",
    chunk_token_num=512,
    min_chunk_tokens=50
)
```

## 第四章 最佳实践

### 4.1 选择合适的策略

根据文档类型选择：
- 技术文档：使用advanced策略
- 简单文本：使用smart策略
- 结构化文档：使用strict_regex策略

### 4.2 参数调优

建议的参数范围：
- chunk_token_num: 256-512
- min_chunk_tokens: 10-50
- overlap_ratio: 0.0-0.2

## 总结

本文档介绍了RAGFlow分块器的使用方法和最佳实践。通过合理的配置和使用，可以获得高质量的文档分块结果。

---

*注：这是一个测试文档，包含了各种markdown元素用于测试分块功能。*
