#!/usr/bin/env python3
"""
RAGFlow本地分块上传工具

功能：
1. 使用本地chunking_utils中的分块函数对文档进行分块
2. 通过RAGFlow API将分块上传到指定的dataset和document
3. 支持多种分块策略：smart, advanced, basic, strict_regex

使用方法：
python ragflow_chunker.py --file document.md --dataset-id your_dataset_id --document-id your_document_id --api-key your_api_key --base-url http://localhost:9380
"""

import argparse
import requests
import json
import os
import sys
import time
import datetime
from typing import List, Dict, Optional
from chunking_utils import (
    split_markdown_to_chunks_smart,
    split_markdown_to_chunks_advanced,
    split_markdown_to_chunks,
    split_markdown_to_chunks_configured,
    split_markdown_to_chunks_strict_regex,
    num_tokens_from_string
)

class RAGFlowChunker:
    def __init__(self, api_key: str, base_url: str = "http://localhost:9380"):
        """
        初始化RAGFlow分块上传器
        
        Args:
            api_key: RAGFlow API密钥
            base_url: RAGFlow服务器地址
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }
    
    def read_file(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            raise Exception(f"读取文件失败: {e}")
    
    def chunk_document(self, content: str, strategy: str = 'smart', 
                      chunk_token_num: int = 256, min_chunk_tokens: int = 10,
                      **kwargs) -> List[str]:
        """
        使用指定策略对文档进行分块
        
        Args:
            content: 文档内容
            strategy: 分块策略 ('smart', 'advanced', 'basic', 'strict_regex')
            chunk_token_num: 目标分块大小（tokens）
            min_chunk_tokens: 最小分块大小（tokens）
            **kwargs: 其他参数
            
        Returns:
            分块列表
        """
        print(f"📝 开始分块处理...")
        print(f"📊 文档长度: {len(content)} 字符")
        print(f"🎯 分块策略: {strategy}")
        print(f"🔢 目标token数: {chunk_token_num}")
        
        if strategy == 'smart':
            chunks = split_markdown_to_chunks_smart(
                content, chunk_token_num, min_chunk_tokens
            )
        elif strategy == 'advanced':
            chunks = split_markdown_to_chunks_advanced(
                content, 
                chunk_token_num=chunk_token_num,
                min_chunk_tokens=min_chunk_tokens,
                overlap_ratio=kwargs.get('overlap_ratio', 0.0),
                include_metadata=kwargs.get('include_metadata', False)
            )
        elif strategy == 'basic':
            chunks = split_markdown_to_chunks(
                content,
                chunk_token_num=chunk_token_num,
                delimiter=kwargs.get('delimiter', "\n!?。；！？")
            )
        elif strategy == 'configured':
            chunks = split_markdown_to_chunks_configured(
                content,
                strategy=kwargs.get('sub_strategy', 'smart'),
                chunk_token_num=chunk_token_num,
                min_chunk_tokens=min_chunk_tokens,
                **kwargs
            )
        elif strategy == 'strict_regex':
            chunks = split_markdown_to_chunks_strict_regex(
                content,
                chunk_token_num=chunk_token_num,
                min_chunk_tokens=min_chunk_tokens,
                regex_pattern=kwargs.get('regex_pattern', r'^#{1,3}\s+')
            )
        else:
            raise ValueError(f"不支持的分块策略: {strategy}")
        
        print(f"✅ 分块完成: {len(chunks)} 个分块")
        if chunks:
            token_counts = [num_tokens_from_string(chunk) for chunk in chunks]
            print(f"📈 Token分布: {min(token_counts)}-{max(token_counts)} (平均: {sum(token_counts)/len(token_counts):.1f})")
        
        return chunks
    
    def add_chunk_to_ragflow(self, dataset_id: str, document_id: str,
                           content: str, important_keywords: List[str] = None,
                           questions: List[str] = None, max_retries: int = 3,
                           retry_delay: float = 1.0) -> Dict:
        """
        向RAGFlow添加单个分块（带重试机制）

        Args:
            dataset_id: 数据集ID
            document_id: 文档ID
            content: 分块内容
            important_keywords: 重要关键词列表
            questions: 问题列表
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）

        Returns:
            API响应结果
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"

        payload = {
            "content": content
        }

        if important_keywords:
            payload["important_keywords"] = important_keywords
        if questions:
            payload["questions"] = questions

        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    print(f"🔄 重试第 {attempt} 次...")
                    time.sleep(retry_delay * attempt)  # 递增延迟

                response = requests.post(url, headers=self.headers, json=payload, timeout=30)
                response.raise_for_status()
                result = response.json()

                if result.get('code') == 0:
                    return result
                else:
                    # API返回错误码，但不是网络错误
                    if attempt == max_retries:
                        return result  # 返回错误结果，让上层处理
                    else:
                        print(f"⚠️ API返回错误 (尝试 {attempt + 1}/{max_retries + 1}): {result.get('message', '未知错误')}")
                        last_exception = Exception(f"API错误: {result.get('message', '未知错误')}")
                        continue

            except requests.exceptions.Timeout as e:
                last_exception = e
                print(f"⏰ 请求超时 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
            except requests.exceptions.ConnectionError as e:
                last_exception = e
                print(f"🔌 连接错误 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
            except requests.exceptions.RequestException as e:
                last_exception = e
                print(f"🌐 网络请求错误 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
            except Exception as e:
                last_exception = e
                print(f"❌ 未知错误 (尝试 {attempt + 1}/{max_retries + 1}): {e}")

        # 所有重试都失败了
        raise Exception(f"上传失败，已重试 {max_retries} 次: {last_exception}")

    def save_failed_chunks(self, failed_chunks: List[Dict], dataset_id: str, document_id: str) -> str:
        """
        保存失败的分块到本地文件

        Args:
            failed_chunks: 失败的分块列表
            dataset_id: 数据集ID
            document_id: 文档ID

        Returns:
            保存的文件路径
        """
        if not failed_chunks:
            return None

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"failed_chunks_{dataset_id}_{document_id}_{timestamp}.json"

        # 创建失败分块目录
        failed_dir = "failed_chunks"
        os.makedirs(failed_dir, exist_ok=True)
        filepath = os.path.join(failed_dir, filename)

        # 准备保存的数据
        save_data = {
            "timestamp": datetime.datetime.now().isoformat(),
            "dataset_id": dataset_id,
            "document_id": document_id,
            "total_failed": len(failed_chunks),
            "failed_chunks": failed_chunks,
            "retry_info": {
                "can_retry": True,
                "retry_command": f"python ragflow_chunker.py --retry-file {filepath}"
            }
        }

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            print(f"💾 失败分块已保存到: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 保存失败分块时出错: {e}")
            return None

    def upload_chunks(self, dataset_id: str, document_id: str, chunks: List[str],
                     important_keywords: List[str] = None,
                     questions: List[str] = None,
                     max_retries: int = 3, retry_delay: float = 1.0) -> Dict:
        """
        批量上传分块到RAGFlow（带重试和失败保存）

        Args:
            dataset_id: 数据集ID
            document_id: 文档ID
            chunks: 分块列表
            important_keywords: 重要关键词列表
            questions: 问题列表
            max_retries: 最大重试次数
            retry_delay: 重试间隔

        Returns:
            上传结果统计
        """
        print(f"🚀 开始上传 {len(chunks)} 个分块到RAGFlow...")
        print(f"🔧 重试配置: 最大重试 {max_retries} 次, 间隔 {retry_delay} 秒")

        results = []
        failed_chunks = []

        for i, chunk in enumerate(chunks, 1):
            try:
                print(f"📤 上传分块 {i}/{len(chunks)} (长度: {len(chunk)} 字符, {num_tokens_from_string(chunk)} tokens)")

                result = self.add_chunk_to_ragflow(
                    dataset_id=dataset_id,
                    document_id=document_id,
                    content=chunk,
                    important_keywords=important_keywords,
                    questions=questions,
                    max_retries=max_retries,
                    retry_delay=retry_delay
                )

                if result.get('code') == 0:
                    chunk_info = result.get('data', {}).get('chunk', {})
                    print(f"✅ 分块 {i} 上传成功 (ID: {chunk_info.get('id', 'N/A')})")
                    results.append(result)
                else:
                    print(f"❌ 分块 {i} 上传失败: {result.get('message', '未知错误')}")
                    failed_chunks.append({
                        'index': i,
                        'chunk': chunk,
                        'error': result.get('message', '未知错误'),
                        'important_keywords': important_keywords,
                        'questions': questions,
                        'timestamp': datetime.datetime.now().isoformat()
                    })

            except Exception as e:
                print(f"❌ 分块 {i} 上传异常: {e}")
                failed_chunks.append({
                    'index': i,
                    'chunk': chunk,
                    'error': str(e),
                    'important_keywords': important_keywords,
                    'questions': questions,
                    'timestamp': datetime.datetime.now().isoformat()
                })

        print(f"📊 上传完成: {len(results)} 成功, {len(failed_chunks)} 失败")

        # 保存失败的分块
        failed_file = None
        if failed_chunks:
            print("❌ 失败的分块:")
            for failed in failed_chunks:
                print(f"  - 分块 {failed['index']}: {failed['error']}")

            failed_file = self.save_failed_chunks(failed_chunks, dataset_id, document_id)
            if failed_file:
                print(f"💡 可以使用以下命令重试失败的分块:")
                print(f"   python ragflow_chunker.py --retry-file {failed_file}")

        return {
            'success_count': len(results),
            'failed_count': len(failed_chunks),
            'total_count': len(chunks),
            'success_results': results,
            'failed_chunks': failed_chunks,
            'failed_file': failed_file
        }

    def retry_failed_chunks(self, failed_file_path: str, max_retries: int = 3,
                           retry_delay: float = 1.0) -> Dict:
        """
        重试失败的分块

        Args:
            failed_file_path: 失败分块文件路径
            max_retries: 最大重试次数
            retry_delay: 重试间隔

        Returns:
            重试结果
        """
        print(f"🔄 开始重试失败的分块: {failed_file_path}")

        try:
            with open(failed_file_path, 'r', encoding='utf-8') as f:
                failed_data = json.load(f)
        except Exception as e:
            raise Exception(f"读取失败分块文件失败: {e}")

        dataset_id = failed_data.get('dataset_id')
        document_id = failed_data.get('document_id')
        failed_chunks = failed_data.get('failed_chunks', [])

        if not failed_chunks:
            print("⚠️ 没有找到失败的分块")
            return {'success_count': 0, 'failed_count': 0, 'total_count': 0}

        print(f"📊 找到 {len(failed_chunks)} 个失败的分块")

        # 提取分块内容和元数据
        chunks_to_retry = []
        for failed_chunk in failed_chunks:
            chunks_to_retry.append({
                'content': failed_chunk['chunk'],
                'important_keywords': failed_chunk.get('important_keywords'),
                'questions': failed_chunk.get('questions'),
                'original_index': failed_chunk.get('index')
            })

        # 重试上传
        retry_results = []
        still_failed = []

        for i, chunk_info in enumerate(chunks_to_retry, 1):
            try:
                print(f"🔄 重试分块 {i}/{len(chunks_to_retry)} (原索引: {chunk_info['original_index']})")

                result = self.add_chunk_to_ragflow(
                    dataset_id=dataset_id,
                    document_id=document_id,
                    content=chunk_info['content'],
                    important_keywords=chunk_info['important_keywords'],
                    questions=chunk_info['questions'],
                    max_retries=max_retries,
                    retry_delay=retry_delay
                )

                if result.get('code') == 0:
                    chunk_info_result = result.get('data', {}).get('chunk', {})
                    print(f"✅ 分块重试成功 (ID: {chunk_info_result.get('id', 'N/A')})")
                    retry_results.append(result)
                else:
                    print(f"❌ 分块重试仍然失败: {result.get('message', '未知错误')}")
                    still_failed.append({
                        'index': chunk_info['original_index'],
                        'chunk': chunk_info['content'],
                        'error': result.get('message', '未知错误'),
                        'important_keywords': chunk_info['important_keywords'],
                        'questions': chunk_info['questions'],
                        'timestamp': datetime.datetime.now().isoformat()
                    })

            except Exception as e:
                print(f"❌ 分块重试异常: {e}")
                still_failed.append({
                    'index': chunk_info['original_index'],
                    'chunk': chunk_info['content'],
                    'error': str(e),
                    'important_keywords': chunk_info['important_keywords'],
                    'questions': chunk_info['questions'],
                    'timestamp': datetime.datetime.now().isoformat()
                })

        print(f"📊 重试完成: {len(retry_results)} 成功, {len(still_failed)} 仍然失败")

        # 如果还有失败的，保存新的失败文件
        new_failed_file = None
        if still_failed:
            new_failed_file = self.save_failed_chunks(still_failed, dataset_id, document_id)
            print(f"💾 仍然失败的分块已保存到新文件: {new_failed_file}")
        else:
            print("🎉 所有分块重试成功!")

        return {
            'success_count': len(retry_results),
            'failed_count': len(still_failed),
            'total_count': len(chunks_to_retry),
            'success_results': retry_results,
            'failed_chunks': still_failed,
            'failed_file': new_failed_file
        }

    def process_file(self, file_path: str, dataset_id: str, document_id: str,
                    strategy: str = 'smart', chunk_token_num: int = 256,
                    min_chunk_tokens: int = 10, max_retries: int = 3,
                    retry_delay: float = 1.0, **kwargs) -> Dict:
        """
        处理文件：读取 -> 分块 -> 上传（带重试和失败保存）

        Args:
            file_path: 文件路径
            dataset_id: 数据集ID
            document_id: 文档ID
            strategy: 分块策略
            chunk_token_num: 目标分块大小
            min_chunk_tokens: 最小分块大小
            max_retries: 最大重试次数
            retry_delay: 重试间隔
            **kwargs: 其他参数

        Returns:
            处理结果统计
        """
        print("=" * 80)
        print("🎯 RAGFlow本地分块上传工具 (增强版)")
        print("=" * 80)

        # 读取文件
        print(f"📖 读取文件: {file_path}")
        content = self.read_file(file_path)

        # 分块处理
        chunks = self.chunk_document(
            content=content,
            strategy=strategy,
            chunk_token_num=chunk_token_num,
            min_chunk_tokens=min_chunk_tokens,
            **kwargs
        )

        if not chunks:
            print("⚠️ 没有生成任何分块")
            return {'success': False, 'message': '没有生成任何分块'}

        # 上传分块（带重试）
        upload_result = self.upload_chunks(
            dataset_id=dataset_id,
            document_id=document_id,
            chunks=chunks,
            important_keywords=kwargs.get('important_keywords'),
            questions=kwargs.get('questions'),
            max_retries=max_retries,
            retry_delay=retry_delay
        )

        return {
            'success': upload_result['success_count'] > 0,
            'total_chunks': upload_result['total_count'],
            'uploaded_chunks': upload_result['success_count'],
            'failed_chunks': upload_result['failed_count'],
            'failed_file': upload_result.get('failed_file'),
            'upload_result': upload_result
        }

def main():
    parser = argparse.ArgumentParser(description='RAGFlow本地分块上传工具 (增强版)')

    # 必需参数
    parser.add_argument('--file', '-f', help='要处理的文档文件路径')
    parser.add_argument('--dataset-id', '-d', help='RAGFlow数据集ID')
    parser.add_argument('--document-id', '-doc', help='RAGFlow文档ID')
    parser.add_argument('--api-key', '-k', help='RAGFlow API密钥')

    # 重试功能
    parser.add_argument('--retry-file', '-rf', help='重试失败分块文件路径')
    
    # 可选参数
    parser.add_argument('--base-url', '-u', default='http://localhost:9380', 
                       help='RAGFlow服务器地址 (默认: http://localhost:9380)')
    parser.add_argument('--strategy', '-s', default='smart', 
                       choices=['smart', 'advanced', 'basic', 'configured'],
                       help='分块策略 (默认: smart)')
    parser.add_argument('--chunk-tokens', '-ct', type=int, default=256,
                       help='目标分块大小(tokens) (默认: 256)')
    parser.add_argument('--min-tokens', '-mt', type=int, default=10,
                       help='最小分块大小(tokens) (默认: 10)')
    parser.add_argument('--keywords', nargs='*', help='重要关键词列表')
    parser.add_argument('--questions', nargs='*', help='问题列表')

    # 重试参数
    parser.add_argument('--max-retries', '-mr', type=int, default=3,
                       help='最大重试次数 (默认: 3)')
    parser.add_argument('--retry-delay', '-rd', type=float, default=1.0,
                       help='重试间隔秒数 (默认: 1.0)')

    # 高级参数
    parser.add_argument('--overlap-ratio', type=float, default=0.0,
                       help='重叠比例 (仅用于advanced策略)')
    parser.add_argument('--include-metadata', action='store_true',
                       help='包含元数据 (仅用于advanced策略)')
    parser.add_argument('--delimiter', default="\n!?。；！？",
                       help='分隔符 (仅用于basic策略)')
    parser.add_argument('--regex-pattern', help='正则表达式模式 (用于configured策略)')
    parser.add_argument('--sub-strategy', default='smart',
                       help='子策略 (用于configured策略)')
    
    args = parser.parse_args()

    try:
        # 处理重试失败分块的情况
        if args.retry_file:
            if not os.path.exists(args.retry_file):
                print(f"❌ 重试文件不存在: {args.retry_file}")
                sys.exit(1)

            # 从重试文件中读取API配置（如果没有在命令行指定）
            if not args.api_key:
                try:
                    with open(args.retry_file, 'r', encoding='utf-8') as f:
                        retry_data = json.load(f)
                    print("💡 从重试文件中读取配置信息...")
                except Exception as e:
                    print(f"❌ 无法读取重试文件配置: {e}")
                    print("请在命令行中指定 --api-key 和 --base-url")
                    sys.exit(1)

            # 创建分块器
            chunker = RAGFlowChunker(
                api_key=args.api_key or "from_retry_file",
                base_url=args.base_url
            )

            # 重试失败的分块
            print("🔄 开始重试失败的分块...")
            retry_result = chunker.retry_failed_chunks(
                failed_file_path=args.retry_file,
                max_retries=args.max_retries,
                retry_delay=args.retry_delay
            )

            # 输出重试结果
            print("=" * 80)
            print("🔄 重试完成!")
            print(f"📊 重试分块数: {retry_result['total_count']}")
            print(f"✅ 重试成功: {retry_result['success_count']}")
            print(f"❌ 仍然失败: {retry_result['failed_count']}")
            if retry_result.get('failed_file'):
                print(f"💾 新的失败文件: {retry_result['failed_file']}")
            print("=" * 80)

            if retry_result['failed_count'] > 0:
                sys.exit(1)
            else:
                sys.exit(0)

        # 正常处理文件的情况
        if not args.file or not args.dataset_id or not args.document_id or not args.api_key:
            print("❌ 缺少必需参数。正常模式需要: --file, --dataset-id, --document-id, --api-key")
            parser.print_help()
            sys.exit(1)

        # 检查文件是否存在
        if not os.path.exists(args.file):
            print(f"❌ 文件不存在: {args.file}")
            sys.exit(1)

        # 创建分块器
        chunker = RAGFlowChunker(api_key=args.api_key, base_url=args.base_url)

        # 准备参数
        kwargs = {
            'important_keywords': args.keywords,
            'questions': args.questions,
            'overlap_ratio': args.overlap_ratio,
            'include_metadata': args.include_metadata,
            'delimiter': args.delimiter,
            'regex_pattern': args.regex_pattern,
            'sub_strategy': args.sub_strategy
        }

        # 处理文件
        result = chunker.process_file(
            file_path=args.file,
            dataset_id=args.dataset_id,
            document_id=args.document_id,
            strategy=args.strategy,
            chunk_token_num=args.chunk_tokens,
            min_chunk_tokens=args.min_tokens,
            max_retries=args.max_retries,
            retry_delay=args.retry_delay,
            **kwargs
        )

        # 输出结果
        if result['success']:
            print("=" * 80)
            print("🎉 处理完成!")
            print(f"📊 总分块数: {result['total_chunks']}")
            print(f"✅ 成功上传: {result['uploaded_chunks']}")
            print(f"❌ 失败数量: {result['failed_chunks']}")
            if result.get('failed_file'):
                print(f"💾 失败分块文件: {result['failed_file']}")
                print(f"💡 重试命令: python ragflow_chunker.py --retry-file {result['failed_file']} --api-key {args.api_key} --base-url {args.base_url}")
            print("=" * 80)

            # 如果有失败的分块，返回非零退出码
            if result['failed_chunks'] > 0:
                sys.exit(1)
        else:
            print(f"❌ 处理失败: {result['message']}")
            sys.exit(1)

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
