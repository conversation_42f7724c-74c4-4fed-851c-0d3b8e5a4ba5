#!/usr/bin/env python3
"""
RAGFlow本地分块上传工具

功能：
1. 使用本地chunking_utils中的分块函数对文档进行分块
2. 通过RAGFlow API将分块上传到指定的dataset和document
3. 支持多种分块策略：smart, advanced, basic, strict_regex

使用方法：
python ragflow_chunker.py --file document.md --dataset-id your_dataset_id --document-id your_document_id --api-key your_api_key --base-url http://localhost:9380
"""

import argparse
import requests
import json
import os
import sys
from typing import List, Dict, Optional
from chunking_utils import (
    split_markdown_to_chunks_smart,
    split_markdown_to_chunks_advanced,
    split_markdown_to_chunks,
    split_markdown_to_chunks_configured,
    num_tokens_from_string
)

class RAGFlowChunker:
    def __init__(self, api_key: str, base_url: str = "http://localhost:9380"):
        """
        初始化RAGFlow分块上传器
        
        Args:
            api_key: RAGFlow API密钥
            base_url: RAGFlow服务器地址
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }
    
    def read_file(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            raise Exception(f"读取文件失败: {e}")
    
    def chunk_document(self, content: str, strategy: str = 'smart', 
                      chunk_token_num: int = 256, min_chunk_tokens: int = 10,
                      **kwargs) -> List[str]:
        """
        使用指定策略对文档进行分块
        
        Args:
            content: 文档内容
            strategy: 分块策略 ('smart', 'advanced', 'basic', 'strict_regex')
            chunk_token_num: 目标分块大小（tokens）
            min_chunk_tokens: 最小分块大小（tokens）
            **kwargs: 其他参数
            
        Returns:
            分块列表
        """
        print(f"📝 开始分块处理...")
        print(f"📊 文档长度: {len(content)} 字符")
        print(f"🎯 分块策略: {strategy}")
        print(f"🔢 目标token数: {chunk_token_num}")
        
        if strategy == 'smart':
            chunks = split_markdown_to_chunks_smart(
                content, chunk_token_num, min_chunk_tokens
            )
        elif strategy == 'advanced':
            chunks = split_markdown_to_chunks_advanced(
                content, 
                chunk_token_num=chunk_token_num,
                min_chunk_tokens=min_chunk_tokens,
                overlap_ratio=kwargs.get('overlap_ratio', 0.0),
                include_metadata=kwargs.get('include_metadata', False)
            )
        elif strategy == 'basic':
            chunks = split_markdown_to_chunks(
                content,
                chunk_token_num=chunk_token_num,
                delimiter=kwargs.get('delimiter', "\n!?。；！？")
            )
        elif strategy == 'configured':
            chunks = split_markdown_to_chunks_configured(
                content,
                strategy=kwargs.get('sub_strategy', 'smart'),
                chunk_token_num=chunk_token_num,
                min_chunk_tokens=min_chunk_tokens,
                **kwargs
            )
        else:
            raise ValueError(f"不支持的分块策略: {strategy}")
        
        print(f"✅ 分块完成: {len(chunks)} 个分块")
        if chunks:
            token_counts = [num_tokens_from_string(chunk) for chunk in chunks]
            print(f"📈 Token分布: {min(token_counts)}-{max(token_counts)} (平均: {sum(token_counts)/len(token_counts):.1f})")
        
        return chunks
    
    def add_chunk_to_ragflow(self, dataset_id: str, document_id: str, 
                           content: str, important_keywords: List[str] = None,
                           questions: List[str] = None) -> Dict:
        """
        向RAGFlow添加单个分块
        
        Args:
            dataset_id: 数据集ID
            document_id: 文档ID
            content: 分块内容
            important_keywords: 重要关键词列表
            questions: 问题列表
            
        Returns:
            API响应结果
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        
        payload = {
            "content": content
        }
        
        if important_keywords:
            payload["important_keywords"] = important_keywords
        if questions:
            payload["questions"] = questions
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API请求失败: {e}")
    
    def upload_chunks(self, dataset_id: str, document_id: str, chunks: List[str],
                     important_keywords: List[str] = None, 
                     questions: List[str] = None,
                     batch_size: int = 10) -> List[Dict]:
        """
        批量上传分块到RAGFlow
        
        Args:
            dataset_id: 数据集ID
            document_id: 文档ID
            chunks: 分块列表
            important_keywords: 重要关键词列表
            questions: 问题列表
            batch_size: 批处理大小
            
        Returns:
            上传结果列表
        """
        print(f"🚀 开始上传 {len(chunks)} 个分块到RAGFlow...")
        
        results = []
        failed_chunks = []
        
        for i, chunk in enumerate(chunks, 1):
            try:
                print(f"📤 上传分块 {i}/{len(chunks)} (长度: {len(chunk)} 字符, {num_tokens_from_string(chunk)} tokens)")
                
                result = self.add_chunk_to_ragflow(
                    dataset_id=dataset_id,
                    document_id=document_id,
                    content=chunk,
                    important_keywords=important_keywords,
                    questions=questions
                )
                
                if result.get('code') == 0:
                    chunk_info = result.get('data', {}).get('chunk', {})
                    print(f"✅ 分块 {i} 上传成功 (ID: {chunk_info.get('id', 'N/A')})")
                    results.append(result)
                else:
                    print(f"❌ 分块 {i} 上传失败: {result.get('message', '未知错误')}")
                    failed_chunks.append({'index': i, 'chunk': chunk, 'error': result.get('message')})
                
            except Exception as e:
                print(f"❌ 分块 {i} 上传异常: {e}")
                failed_chunks.append({'index': i, 'chunk': chunk, 'error': str(e)})
        
        print(f"📊 上传完成: {len(results)} 成功, {len(failed_chunks)} 失败")
        
        if failed_chunks:
            print("❌ 失败的分块:")
            for failed in failed_chunks:
                print(f"  - 分块 {failed['index']}: {failed['error']}")
        
        return results
    
    def process_file(self, file_path: str, dataset_id: str, document_id: str,
                    strategy: str = 'smart', chunk_token_num: int = 256,
                    min_chunk_tokens: int = 10, **kwargs) -> Dict:
        """
        处理文件：读取 -> 分块 -> 上传
        
        Args:
            file_path: 文件路径
            dataset_id: 数据集ID
            document_id: 文档ID
            strategy: 分块策略
            chunk_token_num: 目标分块大小
            min_chunk_tokens: 最小分块大小
            **kwargs: 其他参数
            
        Returns:
            处理结果统计
        """
        print("=" * 80)
        print("🎯 RAGFlow本地分块上传工具")
        print("=" * 80)
        
        # 读取文件
        print(f"📖 读取文件: {file_path}")
        content = self.read_file(file_path)
        
        # 分块处理
        chunks = self.chunk_document(
            content=content,
            strategy=strategy,
            chunk_token_num=chunk_token_num,
            min_chunk_tokens=min_chunk_tokens,
            **kwargs
        )
        
        if not chunks:
            print("⚠️ 没有生成任何分块")
            return {'success': False, 'message': '没有生成任何分块'}
        
        # 上传分块
        results = self.upload_chunks(
            dataset_id=dataset_id,
            document_id=document_id,
            chunks=chunks,
            important_keywords=kwargs.get('important_keywords'),
            questions=kwargs.get('questions')
        )
        
        return {
            'success': True,
            'total_chunks': len(chunks),
            'uploaded_chunks': len(results),
            'failed_chunks': len(chunks) - len(results),
            'results': results
        }

def main():
    parser = argparse.ArgumentParser(description='RAGFlow本地分块上传工具')
    
    # 必需参数
    parser.add_argument('--file', '-f', required=True, help='要处理的文档文件路径')
    parser.add_argument('--dataset-id', '-d', required=True, help='RAGFlow数据集ID')
    parser.add_argument('--document-id', '-doc', required=True, help='RAGFlow文档ID')
    parser.add_argument('--api-key', '-k', required=True, help='RAGFlow API密钥')
    
    # 可选参数
    parser.add_argument('--base-url', '-u', default='http://localhost:9380', 
                       help='RAGFlow服务器地址 (默认: http://localhost:9380)')
    parser.add_argument('--strategy', '-s', default='smart', 
                       choices=['smart', 'advanced', 'basic', 'configured'],
                       help='分块策略 (默认: smart)')
    parser.add_argument('--chunk-tokens', '-ct', type=int, default=256,
                       help='目标分块大小(tokens) (默认: 256)')
    parser.add_argument('--min-tokens', '-mt', type=int, default=10,
                       help='最小分块大小(tokens) (默认: 10)')
    parser.add_argument('--keywords', nargs='*', help='重要关键词列表')
    parser.add_argument('--questions', nargs='*', help='问题列表')
    
    # 高级参数
    parser.add_argument('--overlap-ratio', type=float, default=0.0,
                       help='重叠比例 (仅用于advanced策略)')
    parser.add_argument('--include-metadata', action='store_true',
                       help='包含元数据 (仅用于advanced策略)')
    parser.add_argument('--delimiter', default="\n!?。；！？",
                       help='分隔符 (仅用于basic策略)')
    parser.add_argument('--regex-pattern', help='正则表达式模式 (用于configured策略)')
    parser.add_argument('--sub-strategy', default='smart',
                       help='子策略 (用于configured策略)')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.file):
        print(f"❌ 文件不存在: {args.file}")
        sys.exit(1)
    
    try:
        # 创建分块器
        chunker = RAGFlowChunker(api_key=args.api_key, base_url=args.base_url)
        
        # 准备参数
        kwargs = {
            'important_keywords': args.keywords,
            'questions': args.questions,
            'overlap_ratio': args.overlap_ratio,
            'include_metadata': args.include_metadata,
            'delimiter': args.delimiter,
            'regex_pattern': args.regex_pattern,
            'sub_strategy': args.sub_strategy
        }
        
        # 处理文件
        result = chunker.process_file(
            file_path=args.file,
            dataset_id=args.dataset_id,
            document_id=args.document_id,
            strategy=args.strategy,
            chunk_token_num=args.chunk_tokens,
            min_chunk_tokens=args.min_tokens,
            **kwargs
        )
        
        # 输出结果
        if result['success']:
            print("=" * 80)
            print("🎉 处理完成!")
            print(f"📊 总分块数: {result['total_chunks']}")
            print(f"✅ 成功上传: {result['uploaded_chunks']}")
            print(f"❌ 失败数量: {result['failed_chunks']}")
            print("=" * 80)
        else:
            print(f"❌ 处理失败: {result['message']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
