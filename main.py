# main.py
import os
from dotenv import load_dotenv
from chunking_utils import split_markdown_to_chunks_configured
from ragflow_client import RAGFlowClient
import chunking_utils # 导入整个模块以便访问 num_tokens_from_string
import time

def main():
    load_dotenv() # 加载 .env 文件中的环境变量

    ragflow_base_url = os.getenv("RAGFLOW_BASE_URL")
    ragflow_api_key = os.getenv("RAGFLOW_API_KEY")
    ragflow_dataset_id = os.getenv("RAGFLOW_KNOWLEDGE_BASE_ID")  # 在RAGFlow中，知识库称为dataset

    if not all([ragflow_base_url, ragflow_api_key, ragflow_dataset_id]):
        print("Error: Please set RAGFLOW_BASE_URL, RAGFLOW_API_KEY, and RAGFLOW_KNOWLEDGE_BASE_ID in your .env file.")
        return

    client = RAGFlowClient(ragflow_base_url, ragflow_api_key)

    # 首先获取数据集信息
    print("\n--- 获取数据集信息 ---")
    dataset_info = client.get_dataset_info(ragflow_dataset_id)
    if not dataset_info:
        print("无法获取数据集信息，请检查RAGFLOW_KNOWLEDGE_BASE_ID是否正确")
        return

    print(f"数据集名称: {dataset_info.get('name', 'Unknown')}")
    print(f"当前chunk数量: {dataset_info.get('chunk_count', 0)}")
    print(f"文档数量: {dataset_info.get('document_count', 0)}")

    # --- 从 test.md 文件中读取内容 ---
    test_file_name = "test.md"
    test_file_path = os.path.join(os.getcwd(), test_file_name)
    
    if not os.path.exists(test_file_path):
        print(f"Error: The file {test_file_path} does not exist. Please create it with some Markdown content.")
        return

    with open(test_file_path, "r", encoding="utf-8") as f:
        document_content = f.read()
    print(f"Read content from {test_file_path} (length: {len(document_content)} chars)")

    # --- 1. 上传文件到 RAGFlow 数据集并创建文档记录 ---
    print("\n--- 1. 上传文件到 RAGFlow 数据集并创建文档记录 ---")
    uploaded_doc_info = client.upload_document_to_dataset(ragflow_dataset_id, test_file_path)

    if not uploaded_doc_info:
        print("文件上传到数据集失败，停止后续操作。")
        return

    doc_id = uploaded_doc_info.get('id')
    file_name = uploaded_doc_info.get('name') # 或者使用 display_name
    if not doc_id:
        print("未从上传响应中获取到文档ID，停止后续操作。")
        return

    print(f"文件 '{file_name}' 已上传并关联到数据集 {ragflow_dataset_id}，Doc ID: {doc_id}")

    # --- 2. 触发文档解析 ---
    print(f"\n--- 2. 触发文档解析 ---")
    parse_result = client.parse_documents(ragflow_dataset_id, [doc_id])

    if not parse_result:
        print("触发文档解析失败，停止后续操作。")
        return

    # --- 3. 等待解析完成 ---
    print(f"\n--- 3. 等待文档解析完成 ---")
    parse_success = client.wait_for_document_completion(ragflow_dataset_id, doc_id, max_wait_time=300)

    if not parse_success:
        print("文档解析未能在指定时间内完成")
        return

    # --- 4. 本地智能分块（用于对比） ---
    strategy = 'advanced' # 使用高级分块策略
    print(f"\n--- 4. 使用 '{strategy}' 策略进行本地分块（用于对比） ---")
    local_chunks_with_metadata = split_markdown_to_chunks_configured(
        document_content, # 使用从 test.md 读取的内容
        strategy=strategy,
        chunk_token_num=256,
        min_chunk_tokens=50,
        include_metadata=True # 确保返回包含元数据的分块
    )

    # 提取纯文本内容
    local_chunks_content = [chunk['content'] for chunk in local_chunks_with_metadata]

    print(f"\n--- 本地分块完成，共生成 {len(local_chunks_content)} 个块 ---")
    for i, chunk_data in enumerate(local_chunks_with_metadata):
        print(f"\n--- Local Chunk {i+1} (Length: {len(chunk_data['content'])} chars, Tokens: {chunking_utils.num_tokens_from_string(chunk_data['content'])}) ---")
        print(chunk_data['content'][:200] + "..." if len(chunk_data['content']) > 200 else chunk_data['content'])
        print(f"  Metadata: {chunk_data.get('metadata')}")
        print(f"  Chunk Type: {chunk_data.get('chunk_type')}")
        print(f"  Has Special Content: {chunk_data.get('has_special_content')}")

    # --- 5. 查看RAGFlow解析的chunks ---
    print(f"\n--- 5. 查看RAGFlow解析的chunks ---")
    ragflow_chunks_result = client.list_chunks(ragflow_dataset_id, doc_id)

    if ragflow_chunks_result:
        data = ragflow_chunks_result.get('data', {})
        ragflow_chunks = data.get('chunks', [])
        total_chunks = data.get('total', 0)
        print(f"RAGFlow解析生成了 {len(ragflow_chunks)} 个chunks (总共: {total_chunks})")

        for i, chunk in enumerate(ragflow_chunks[:5]):  # 只显示前5个
            content = chunk.get('content', '')
            print(f"\n--- RAGFlow Chunk {i+1} (Length: {len(content)} chars) ---")
            print(content[:200] + "..." if len(content) > 200 else content)

    # --- 6. 获取最终数据集信息 ---
    print(f"\n--- 6. 获取最终数据集信息 ---")
    final_dataset_info = client.get_dataset_info(ragflow_dataset_id)
    if final_dataset_info:
        print(f"最终chunk数量: {final_dataset_info.get('chunk_count', 0)}")
        print(f"最终文档数量: {final_dataset_info.get('document_count', 0)}")

    print("\n--- RAGFlow 文档处理流程完成 ---")


if __name__ == "__main__":
    main()