# RAGFlow本地分块上传工具

这是一个用于将文档在本地进行分块处理，然后通过RAGFlow API上传chunks的工具。

## 功能特性

- 🚀 **多种分块策略**: 支持smart、advanced、basic、strict_regex等多种分块方法
- 📝 **本地处理**: 使用本地chunking_utils中的高质量分块函数
- 🔄 **批量上传**: 支持批量上传分块到RAGFlow
- 🎯 **灵活配置**: 支持自定义分块参数和上传选项
- 📊 **详细统计**: 提供分块和上传的详细统计信息
- 🛡️ **智能重试**: 自动重试失败的上传，最大3次重试，递增延迟
- 💾 **失败保存**: 自动保存失败的分块到本地文件，支持后续重试
- 🔧 **健壮性强**: 完善的错误处理，网络异常自动恢复
- 🧠 **KnowFlow增强**: 整合KnowFlow项目的智能分块特性
- 🎛️ **配置化接口**: 统一的分块策略选择和配置管理
- 📏 **智能标题处理**: 避免过度分块，保持语义完整性
- 🔍 **精确正则分块**: 支持自定义正则表达式分块模式

## 安装依赖

```bash
pip install requests markdown markdown-it-py tiktoken
```

## 快速开始

### 1. 命令行使用

```bash
python ragflow_chunker.py \
  --file document.md \
  --dataset-id your_dataset_id \
  --document-id your_document_id \
  --api-key your_api_key \
  --base-url http://localhost:9380 \
  --strategy smart \
  --chunk-tokens 256
```

### 2. Python代码使用

```python
from ragflow_chunker import RAGFlowChunker

# 创建分块器
chunker = RAGFlowChunker(
    api_key="your_api_key",
    base_url="http://localhost:9380"
)

# 处理文件
result = chunker.process_file(
    file_path="document.md",
    dataset_id="your_dataset_id",
    document_id="your_document_id",
    strategy="smart",
    chunk_token_num=256
)

print(f"上传完成: {result['uploaded_chunks']}/{result['total_chunks']}")
```

## 分块策略

### 1. Smart策略 (推荐)
- 基于markdown-it-py AST解析
- 智能处理表格、代码块、列表等结构
- 保持语义完整性

```bash
--strategy smart --chunk-tokens 256 --min-tokens 10
```

### 2. Advanced策略
- 基于标题层级的高级分块
- 支持上下文增强和动态大小控制
- 适合长文档和复杂结构

```bash
--strategy advanced --chunk-tokens 512 --min-tokens 50 --overlap-ratio 0.1
```

### 3. Basic策略
- 基于分隔符的简单分块
- 快速处理，适合简单文档

```bash
--strategy basic --chunk-tokens 256 --delimiter "\n\n"
```

### 4. Configured策略 (KnowFlow增强)
- 支持正则表达式分块
- 配置化分块方法选择
- 文档级别的自定义配置

```bash
--strategy configured --sub-strategy strict_regex --regex-pattern "^#{1,3}\s+"
```

### 5. 配置化分块接口
使用统一的配置化接口，支持动态策略选择：

```python
from chunking_utils import split_markdown_to_chunks_configured

# 基本使用
chunks = split_markdown_to_chunks_configured(
    content,
    strategy='smart',
    chunk_token_num=256
)

# 高级配置
chunks = split_markdown_to_chunks_configured(
    content,
    strategy='advanced',
    chunking_config={
        'chunk_token_num': 512,
        'overlap_ratio': 0.1,
        'include_metadata': True
    }
)
```

## 命令行参数

### 必需参数
- `--file, -f`: 要处理的文档文件路径
- `--dataset-id, -d`: RAGFlow数据集ID
- `--document-id, -doc`: RAGFlow文档ID
- `--api-key, -k`: RAGFlow API密钥

### 可选参数
- `--base-url, -u`: RAGFlow服务器地址 (默认: http://localhost:9380)
- `--strategy, -s`: 分块策略 (默认: smart)
- `--chunk-tokens, -ct`: 目标分块大小 (默认: 256)
- `--min-tokens, -mt`: 最小分块大小 (默认: 10)
- `--keywords`: 重要关键词列表
- `--questions`: 问题列表

### 重试参数
- `--max-retries, -mr`: 最大重试次数 (默认: 3)
- `--retry-delay, -rd`: 重试间隔秒数 (默认: 1.0)
- `--retry-file, -rf`: 重试失败分块文件路径

### 高级参数
- `--overlap-ratio`: 重叠比例 (仅advanced策略)
- `--include-metadata`: 包含元数据 (仅advanced策略)
- `--delimiter`: 分隔符 (仅basic策略)
- `--regex-pattern`: 正则表达式模式
- `--sub-strategy`: 子策略

## 使用示例

### 基本使用
```bash
python ragflow_chunker.py \
  --file example.md \
  --dataset-id "abc123" \
  --document-id "doc456" \
  --api-key "your_key"
```

### 高级分块
```bash
python ragflow_chunker.py \
  --file complex_doc.md \
  --dataset-id "abc123" \
  --document-id "doc456" \
  --api-key "your_key" \
  --strategy advanced \
  --chunk-tokens 512 \
  --min-tokens 50 \
  --overlap-ratio 0.1 \
  --keywords "AI" "RAG" "知识库"
```

### 正则表达式分块
```bash
python ragflow_chunker.py \
  --file structured_doc.md \
  --dataset-id "abc123" \
  --document-id "doc456" \
  --api-key "your_key" \
  --strategy configured \
  --sub-strategy strict_regex \
  --regex-pattern "^#{1,3}\s+"
```

### 重试失败分块
```bash
# 如果上传过程中有分块失败，会自动生成失败文件
# 使用以下命令重试失败的分块
python ragflow_chunker.py \
  --retry-file failed_chunks/failed_chunks_dataset_doc_20240101_120000.json \
  --api-key "your_key" \
  --base-url "http://localhost:9380" \
  --max-retries 5
```

## API参考

### RAGFlowChunker类

#### 初始化
```python
chunker = RAGFlowChunker(api_key, base_url)
```

#### 主要方法

##### process_file()
完整的文件处理流程：读取 -> 分块 -> 上传

```python
result = chunker.process_file(
    file_path="document.md",
    dataset_id="dataset_id",
    document_id="document_id",
    strategy="smart",
    chunk_token_num=256,
    min_chunk_tokens=10,
    **kwargs
)
```

##### chunk_document()
仅进行文档分块

```python
chunks = chunker.chunk_document(
    content="文档内容",
    strategy="smart",
    chunk_token_num=256
)
```

##### upload_chunks()
批量上传分块

```python
results = chunker.upload_chunks(
    dataset_id="dataset_id",
    document_id="document_id",
    chunks=chunks,
    important_keywords=["关键词"],
    questions=["问题"]
)
```

## 配置文件

可以使用`config.json`文件进行配置：

```json
{
  "ragflow": {
    "api_key": "your_api_key_here",
    "base_url": "http://localhost:9380"
  },
  "chunking": {
    "default_strategy": "smart",
    "chunk_token_num": 256,
    "min_chunk_tokens": 10
  }
}
```

## 错误处理和重试机制

工具包含完善的错误处理和重试机制：

### 自动重试
- **网络错误重试**: 自动重试网络连接失败、超时等问题
- **递增延迟**: 重试间隔递增，避免频繁请求
- **最大重试次数**: 默认最大重试3次，可自定义
- **详细日志**: 记录每次重试的详细信息

### 失败分块保存
- **自动保存**: 重试后仍然失败的分块自动保存到本地
- **完整信息**: 保存分块内容、错误信息、时间戳等
- **重试支持**: 支持后续使用`--retry-file`参数重试

### 失败文件格式
```json
{
  "timestamp": "2024-01-01T12:00:00",
  "dataset_id": "dataset_id",
  "document_id": "document_id",
  "total_failed": 2,
  "failed_chunks": [
    {
      "index": 1,
      "chunk": "分块内容",
      "error": "错误信息",
      "important_keywords": ["关键词"],
      "questions": ["问题"],
      "timestamp": "2024-01-01T12:00:00"
    }
  ]
}
```

### 重试命令
```bash
python ragflow_chunker.py \
  --retry-file failed_chunks/failed_chunks_xxx.json \
  --api-key your_key \
  --max-retries 5
```

## 性能优化

- 批量上传减少API调用次数
- 智能分块策略提高处理质量
- 本地处理减少服务器负载
- 支持并发处理（可扩展）

## 注意事项

1. 确保RAGFlow服务正常运行
2. 检查API密钥权限
3. 确认数据集和文档ID存在
4. 大文件建议使用advanced策略
5. 网络不稳定时可调整重试参数

## 故障排除

### 常见问题

**Q: API请求失败**
A: 检查base_url和api_key是否正确，确认RAGFlow服务运行状态

**Q: 分块数量为0**
A: 检查文档内容是否为空，或调整min_chunk_tokens参数

**Q: 上传失败**
A: 确认dataset_id和document_id存在，检查网络连接

**Q: 内存不足**
A: 对于大文件，建议使用较小的chunk_token_num或分批处理

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
