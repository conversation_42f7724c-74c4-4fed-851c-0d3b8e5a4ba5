# ragflow_client.py
import requests
import os
import time
from typing import List, Dict, Any

class RAGFlowClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
        }

    def _make_request(self, method: str, endpoint: str, json_data: Dict = None, files: Dict = None):
        url = f"{self.base_url}/{endpoint}"
        try:
            if method == "POST":
                if files:
                    # 对于文件上传，requests 会自动设置 Content-Type 为 multipart/form-data
                    response = requests.post(url, headers={"Authorization": self.headers["Authorization"]}, files=files)
                else:
                    response = requests.post(url, headers=self.headers, json=json_data)
            elif method == "GET":
                response = requests.get(url, headers=self.headers, params=json_data)
            elif method == "DELETE":
                response = requests.delete(url, headers=self.headers, json=json_data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            return response.json()
        except requests.exceptions.HTTPError as e:
            print(f"HTTP Error: {e.response.status_code} - {e.response.text}")
            raise
        except requests.exceptions.RequestException as e:
            print(f"Request Error: {e}")
            raise

    def upload_document_to_dataset(self, dataset_id: str, file_path: str) -> Dict[str, Any]:
        """
        上传文件到数据集并创建文档记录。
        """
        endpoint = f"datasets/{dataset_id}/documents"
        file_name = os.path.basename(file_path)
        with open(file_path, 'rb') as f:
            files = {'file': (file_name, f)}
            print(f"Uploading file '{file_name}' to dataset {dataset_id}...")
            try:
                response_data = self._make_request("POST", endpoint, files=files)
                if response_data.get('code') == 0:
                    print(f"File '{file_name}' uploaded successfully to dataset {dataset_id}.")
                    # RAGFlow API for upload to dataset returns a list of documents in 'data'
                    # We expect one document for a single file upload
                    return response_data.get('data', [])[0] if response_data.get('data') else {}
                else:
                    print(f"Failed to upload file '{file_name}' to dataset {dataset_id}: {response_data.get('message', 'Unknown error')}")
                    return {}
            except Exception as e:
                print(f"Error uploading file '{file_name}' to dataset {dataset_id}: {e}")
                return {}

    def parse_documents(self, dataset_id: str, document_ids: List[str]) -> Dict[str, Any]:
        """
        触发指定文档的解析过程（包括分块）。
        """
        endpoint = f"datasets/{dataset_id}/chunks"
        payload = {"document_ids": document_ids}
        print(f"Triggering parse for documents {document_ids} in dataset {dataset_id}...")
        try:
            response_data = self._make_request("POST", endpoint, json_data=payload)
            if response_data.get('code') == 0:
                print(f"Documents {document_ids} parse triggered successfully.")
                return response_data
            else:
                print(f"Failed to trigger parse for documents {document_ids}: {response_data.get('message', 'Unknown error')}")
                return {}
        except Exception as e:
            print(f"Error triggering parse for documents {document_ids}: {e}")
            return {}

    def get_document_status(self, dataset_id: str, document_id: str) -> Dict[str, Any]:
        """
        获取指定文档的状态和解析进度。
        """
        endpoint = f"datasets/{dataset_id}/documents"
        params = {"id": document_id}
        print(f"Getting status for document {document_id}...")
        try:
            response_data = self._make_request("GET", endpoint, json_data=params)
            if response_data and response_data.get('code') == 0:
                data = response_data.get('data', {})
                documents = data.get('docs', [])

                # 查找指定的文档
                doc_info = None
                for doc in documents:
                    if doc.get('id') == document_id:
                        doc_info = doc
                        break

                if doc_info:
                    status = doc_info.get('status', 'unknown')
                    progress_msg = doc_info.get('progress_msg', 'N/A')
                    progress = doc_info.get('progress', 0)
                    run_status = doc_info.get('run', 'UNKNOWN')
                    print(f"Document {document_id} status: {status}, run: {run_status}, progress: {progress*100:.1f}%")
                    return doc_info
                else:
                    print(f"Document {document_id} not found in response")
                    return {}
            else:
                print(f"Failed to get status for document {document_id}: {response_data.get('message', 'Unknown error') if response_data else 'No response'}")
                return {}
        except Exception as e:
            print(f"Error getting status for document {document_id}: {e}")
            return {}

    def wait_for_document_completion(self, dataset_id: str, document_id: str, max_wait_time: int = 300, check_interval: int = 10) -> bool:
        """
        等待文档解析完成。

        Args:
            dataset_id: 数据集ID
            document_id: 文档ID
            max_wait_time: 最大等待时间（秒）
            check_interval: 检查间隔（秒）

        Returns:
            bool: 解析是否成功完成
        """
        print(f"Waiting for document {document_id} to complete parsing...")
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            doc_info = self.get_document_status(dataset_id, document_id)
            if not doc_info:
                print(f"Failed to get document status, retrying in {check_interval} seconds...")
                time.sleep(check_interval)
                continue

            run_status = doc_info.get('run', 'UNKNOWN')
            progress = doc_info.get('progress', 0)

            if run_status == 'DONE':  # 解析完成
                print(f"Document {document_id} parsing completed successfully!")
                return True
            elif run_status in ['RUNNING', 'UNSTART']:  # 解析中或未开始
                print(f"Document {document_id} parsing in progress: {progress*100:.1f}%")
            elif run_status == 'FAIL':  # 解析失败
                print(f"Document {document_id} parsing failed!")
                return False
            else:
                print(f"Document {document_id} run status: {run_status}, progress: {progress*100:.1f}%")

            time.sleep(check_interval)

        print(f"Timeout waiting for document {document_id} to complete parsing")
        return False

    def add_chunk_to_document(self, dataset_id: str, document_id: str, content: str) -> Dict[str, Any]:
        """
        向指定文档添加一个chunk。
        """
        endpoint = f"datasets/{dataset_id}/documents/{document_id}/chunks"
        payload = {"content": content}
        print(f"Adding chunk to document {document_id}...")
        try:
            response_data = self._make_request("POST", endpoint, json_data=payload)
            if response_data.get('code') == 0:
                print(f"Chunk added successfully to document {document_id}.")
                return response_data
            else:
                print(f"Failed to add chunk to document {document_id}: {response_data.get('message', 'Unknown error')}")
                return {}
        except Exception as e:
            print(f"Error adding chunk to document {document_id}: {e}")
            return {}

    def list_chunks(self, dataset_id: str, document_id: str, page: int = 1, page_size: int = 30, keywords: str = "") -> Dict[str, Any]:
        """
        列出指定文档中的chunks。
        """
        endpoint = f"datasets/{dataset_id}/documents/{document_id}/chunks"
        params = {"page": page, "page_size": page_size}
        if keywords:
            params["keywords"] = keywords

        print(f"Listing chunks for document {document_id} in dataset {dataset_id}...")
        try:
            response_data = self._make_request("GET", endpoint, json_data=params)
            if response_data.get('code') == 0:
                data = response_data.get('data', {})
                chunks = data.get('chunks', [])
                total = data.get('total', 0)
                print(f"Found {len(chunks)} chunks (total: {total}) in document {document_id}")
                return response_data
            else:
                print(f"Failed to list chunks for document {document_id}: {response_data.get('message', 'Unknown error')}")
                return {}
        except Exception as e:
            print(f"Error listing chunks for document {document_id}: {e}")
            return {}

    def get_dataset_info(self, dataset_id: str) -> Dict[str, Any]:
        """
        获取数据集信息。
        """
        endpoint = f"datasets"
        params = {"id": dataset_id}
        print(f"Getting info for dataset {dataset_id}...")
        try:
            response_data = self._make_request("GET", endpoint, json_data=params)
            if response_data.get('code') == 0:
                datasets = response_data.get('data', [])
                if datasets:
                    dataset_info = datasets[0]
                    print(f"Dataset {dataset_id}: {dataset_info.get('name', 'Unknown')} - {dataset_info.get('chunk_count', 0)} chunks")
                    return dataset_info
                else:
                    print(f"Dataset {dataset_id} not found")
                    return {}
            else:
                print(f"Failed to get info for dataset {dataset_id}: {response_data.get('message', 'Unknown error')}")
                return {}
        except Exception as e:
            print(f"Error getting info for dataset {dataset_id}: {e}")
            return {}