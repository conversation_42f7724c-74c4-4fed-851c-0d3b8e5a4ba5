#!/usr/bin/env python3
# test_api.py - 测试RAGFlow API调用
import os
from dotenv import load_dotenv
from ragflow_client import RAGFlowClient

def test_api():
    load_dotenv()
    
    ragflow_base_url = os.getenv("RAGFLOW_BASE_URL")
    ragflow_api_key = os.getenv("RAGFLOW_API_KEY")
    ragflow_dataset_id = os.getenv("RAGFLOW_KNOWLEDGE_BASE_ID")
    
    client = RAGFlowClient(ragflow_base_url, ragflow_api_key)
    
    # 测试获取数据集信息
    print("=== 测试获取数据集信息 ===")
    dataset_info = client.get_dataset_info(ragflow_dataset_id)
    print(f"Dataset info: {dataset_info}")
    
    # 测试列出文档
    print("\n=== 测试列出文档 ===")
    try:
        endpoint = f"datasets/{ragflow_dataset_id}/documents"
        response_data = client._make_request("GET", endpoint)
        print(f"Documents response: {response_data}")
        
        if response_data and response_data.get('code') == 0:
            documents = response_data.get('data', [])
            print(f"Found {len(documents)} documents")
            for doc in documents[:3]:  # 只显示前3个
                print(f"  Doc ID: {doc.get('id')}, Name: {doc.get('name')}, Status: {doc.get('status')}")
    except Exception as e:
        print(f"Error listing documents: {e}")

if __name__ == "__main__":
    test_api()
