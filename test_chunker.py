#!/usr/bin/env python3
"""
RAGFlow分块器测试脚本

用于测试分块功能，不需要实际的RAGFlow服务器
"""

import os
import json
from ragflow_chunker import RAGFlowChunker
from chunking_utils import (
    split_markdown_to_chunks_smart,
    split_markdown_to_chunks_advanced,
    split_markdown_to_chunks,
    num_tokens_from_string
)

def create_test_document():
    """创建测试文档"""
    test_content = """# 测试文档

## 第一章 介绍

这是一个测试文档，用于验证分块功能。

### 1.1 背景

在自然语言处理和信息检索领域，文档分块是一个重要的预处理步骤。

### 1.2 目标

我们的目标是：
- 保持语义完整性
- 控制分块大小
- 处理特殊结构

## 第二章 技术细节

### 2.1 算法原理

分块算法基于以下原理：

1. **语义边界识别**: 识别自然的语义边界
2. **大小控制**: 控制每个分块的token数量
3. **结构保持**: 保持markdown结构的完整性

### 2.2 实现方法

#### 2.2.1 AST解析

使用markdown-it-py进行AST解析：

```python
from markdown_it import MarkdownIt
md = MarkdownIt()
tokens = md.parse(text)
```

#### 2.2.2 分块策略

支持多种分块策略：

| 策略 | 描述 | 适用场景 |
|------|------|----------|
| Smart | 智能分块 | 一般文档 |
| Advanced | 高级分块 | 复杂文档 |
| Basic | 基础分块 | 简单文档 |

### 2.3 参数配置

重要参数包括：
- `chunk_token_num`: 目标分块大小
- `min_chunk_tokens`: 最小分块大小
- `overlap_ratio`: 重叠比例

## 第三章 使用示例

### 3.1 基本使用

```python
from ragflow_chunker import RAGFlowChunker

chunker = RAGFlowChunker(api_key="key", base_url="url")
result = chunker.process_file("doc.md", "dataset_id", "doc_id")
```

### 3.2 高级配置

可以通过参数进行高级配置：

```python
result = chunker.process_file(
    file_path="document.md",
    dataset_id="dataset_id",
    document_id="document_id",
    strategy="advanced",
    chunk_token_num=512,
    min_chunk_tokens=50
)
```

## 第四章 最佳实践

### 4.1 选择合适的策略

根据文档类型选择：
- 技术文档：使用advanced策略
- 简单文本：使用smart策略
- 结构化文档：使用strict_regex策略

### 4.2 参数调优

建议的参数范围：
- chunk_token_num: 256-512
- min_chunk_tokens: 10-50
- overlap_ratio: 0.0-0.2

## 总结

本文档介绍了RAGFlow分块器的使用方法和最佳实践。通过合理的配置和使用，可以获得高质量的文档分块结果。

---

*注：这是一个测试文档，包含了各种markdown元素用于测试分块功能。*
"""
    
    with open("test_document.md", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    return "test_document.md", test_content

def test_chunking_strategies():
    """测试不同的分块策略"""
    print("=" * 80)
    print("🧪 测试分块策略")
    print("=" * 80)
    
    # 创建测试文档
    file_path, content = create_test_document()
    print(f"📝 创建测试文档: {file_path}")
    print(f"📊 文档长度: {len(content)} 字符, {num_tokens_from_string(content)} tokens")
    
    strategies = [
        {
            'name': 'Smart策略',
            'function': split_markdown_to_chunks_smart,
            'params': {'chunk_token_num': 256, 'min_chunk_tokens': 10}
        },
        {
            'name': 'Advanced策略',
            'function': split_markdown_to_chunks_advanced,
            'params': {'chunk_token_num': 512, 'min_chunk_tokens': 50}
        },
        {
            'name': 'Basic策略',
            'function': split_markdown_to_chunks,
            'params': {'chunk_token_num': 256, 'delimiter': '\n\n'}
        }
    ]
    
    results = {}
    
    for strategy in strategies:
        print(f"\n🎯 测试 {strategy['name']}")
        print("-" * 40)
        
        try:
            chunks = strategy['function'](content, **strategy['params'])
            
            if chunks:
                token_counts = [num_tokens_from_string(chunk) for chunk in chunks]
                results[strategy['name']] = {
                    'chunks': len(chunks),
                    'min_tokens': min(token_counts),
                    'max_tokens': max(token_counts),
                    'avg_tokens': sum(token_counts) / len(token_counts),
                    'total_tokens': sum(token_counts)
                }
                
                print(f"✅ 分块数量: {len(chunks)}")
                print(f"📈 Token分布: {min(token_counts)}-{max(token_counts)} (平均: {sum(token_counts)/len(token_counts):.1f})")
                print(f"📊 总Token数: {sum(token_counts)}")
                
                # 显示前2个分块的预览
                print("👀 分块预览:")
                for i, chunk in enumerate(chunks[:2], 1):
                    preview = chunk.replace('\n', ' ')[:100]
                    print(f"  分块{i}: {preview}...")
            else:
                print("❌ 没有生成分块")
                results[strategy['name']] = {'chunks': 0}
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results[strategy['name']] = {'error': str(e)}
    
    # 输出对比结果
    print("\n" + "=" * 80)
    print("📊 分块策略对比结果")
    print("=" * 80)
    
    print(f"{'策略':<15} {'分块数':<8} {'最小Token':<10} {'最大Token':<10} {'平均Token':<10} {'总Token':<10}")
    print("-" * 80)
    
    for name, result in results.items():
        if 'error' in result:
            print(f"{name:<15} {'错误':<8} {'-':<10} {'-':<10} {'-':<10} {'-':<10}")
        elif result['chunks'] == 0:
            print(f"{name:<15} {'0':<8} {'-':<10} {'-':<10} {'-':<10} {'-':<10}")
        else:
            print(f"{name:<15} {result['chunks']:<8} {result['min_tokens']:<10.0f} {result['max_tokens']:<10.0f} {result['avg_tokens']:<10.1f} {result['total_tokens']:<10.0f}")
    
    return results

def test_chunker_class():
    """测试RAGFlowChunker类的分块功能（不上传）"""
    print("\n" + "=" * 80)
    print("🔧 测试RAGFlowChunker类")
    print("=" * 80)
    
    # 创建分块器（使用虚拟API密钥）
    chunker = RAGFlowChunker(api_key="test_key", base_url="http://test:9380")
    
    file_path, content = create_test_document()
    
    strategies = ['smart', 'advanced', 'basic']
    
    for strategy in strategies:
        print(f"\n🎯 测试 {strategy} 策略")
        print("-" * 40)
        
        try:
            chunks = chunker.chunk_document(
                content=content,
                strategy=strategy,
                chunk_token_num=256,
                min_chunk_tokens=10
            )
            
            if chunks:
                token_counts = [num_tokens_from_string(chunk) for chunk in chunks]
                print(f"✅ 分块成功: {len(chunks)} 个分块")
                print(f"📈 Token分布: {min(token_counts)}-{max(token_counts)} (平均: {sum(token_counts)/len(token_counts):.1f})")
            else:
                print("❌ 没有生成分块")
                
        except Exception as e:
            print(f"❌ 分块失败: {e}")

def test_file_operations():
    """测试文件操作"""
    print("\n" + "=" * 80)
    print("📁 测试文件操作")
    print("=" * 80)
    
    chunker = RAGFlowChunker(api_key="test_key", base_url="http://test:9380")
    
    # 测试读取文件
    file_path, expected_content = create_test_document()
    
    try:
        content = chunker.read_file(file_path)
        print(f"✅ 文件读取成功: {len(content)} 字符")
        
        if content == expected_content:
            print("✅ 文件内容匹配")
        else:
            print("❌ 文件内容不匹配")
            
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
    
    # 测试读取不存在的文件
    try:
        content = chunker.read_file("nonexistent_file.md")
        print("❌ 应该抛出异常但没有")
    except Exception as e:
        print(f"✅ 正确处理不存在的文件: {type(e).__name__}")

def save_test_results(results):
    """保存测试结果"""
    test_results = {
        'timestamp': '2024-01-01T00:00:00Z',
        'chunking_strategies': results,
        'test_document_stats': {
            'file': 'test_document.md',
            'size_chars': len(open('test_document.md', 'r', encoding='utf-8').read()),
            'size_tokens': num_tokens_from_string(open('test_document.md', 'r', encoding='utf-8').read())
        }
    }
    
    with open('test_results.json', 'w', encoding='utf-8') as f:
        json.dump(test_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试结果已保存到: test_results.json")

def main():
    """主测试函数"""
    print("🧪 RAGFlow分块器测试")
    print("=" * 80)
    
    try:
        # 测试分块策略
        results = test_chunking_strategies()
        
        # 测试分块器类
        test_chunker_class()
        
        # 测试文件操作
        test_file_operations()
        
        # 保存测试结果
        save_test_results(results)
        
        print("\n" + "=" * 80)
        print("🎉 所有测试完成!")
        print("=" * 80)
        print("📝 生成的文件:")
        print("  - test_document.md: 测试文档")
        print("  - test_results.json: 测试结果")
        print("\n💡 提示:")
        print("  - 查看test_document.md了解测试内容")
        print("  - 查看test_results.json了解测试结果")
        print("  - 修改ragflow_chunker.py中的API配置后可进行实际上传测试")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
