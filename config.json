{"ragflow": {"api_key": "your_api_key_here", "base_url": "http://localhost:9380", "timeout": 30}, "chunking": {"default_strategy": "smart", "chunk_token_num": 256, "min_chunk_tokens": 10, "strategies": {"smart": {"description": "智能分块策略，基于AST解析", "chunk_token_num": 256, "min_chunk_tokens": 10}, "advanced": {"description": "高级分块策略，支持标题层级和上下文增强", "chunk_token_num": 512, "min_chunk_tokens": 50, "overlap_ratio": 0.0, "include_metadata": false}, "basic": {"description": "基础分块策略，基于分隔符", "chunk_token_num": 256, "delimiter": "\n!?。；！？"}, "strict_regex": {"description": "严格正则表达式分块", "chunk_token_num": 512, "min_chunk_tokens": 20, "regex_pattern": "^#{1,3}\\s+"}}}, "upload": {"batch_size": 10, "retry_count": 3, "retry_delay": 1}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}