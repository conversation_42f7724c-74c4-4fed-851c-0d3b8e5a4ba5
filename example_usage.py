#!/usr/bin/env python3
"""
RAGFlow分块上传工具使用示例

展示如何使用RAGFlowChunker类进行文档分块和上传
"""

import os
from ragflow_chunker import RAGFlowChunker

def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("📝 基本使用示例")
    print("=" * 60)
    
    # 配置参数
    api_key = "your_api_key_here"  # 替换为您的API密钥
    base_url = "http://localhost:9380"  # 替换为您的RAGFlow服务器地址
    dataset_id = "your_dataset_id"  # 替换为您的数据集ID
    document_id = "your_document_id"  # 替换为您的文档ID
    file_path = "example_document.md"  # 替换为您的文档路径
    
    # 创建分块器
    chunker = RAGFlowChunker(api_key=api_key, base_url=base_url)
    
    # 处理文件
    try:
        result = chunker.process_file(
            file_path=file_path,
            dataset_id=dataset_id,
            document_id=document_id,
            strategy='smart',  # 使用智能分块策略
            chunk_token_num=256,  # 目标256 tokens
            min_chunk_tokens=10   # 最小10 tokens
        )
        
        print(f"✅ 处理完成: {result}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")

def example_advanced_usage():
    """高级使用示例"""
    print("=" * 60)
    print("🚀 高级使用示例")
    print("=" * 60)
    
    # 配置参数
    api_key = "ragflow-Y3M2YzOTZlMWZlNjExZjBhOWUyMDI0Mm"
    base_url = "http://10.15.1.75/api/v1"
    dataset_id = "f14812aa57ba11f08ea30242ac1e0806"
    document_id = "9cc36a6057c211f0876b0242ac1e0806"
    file_path = "/Users/<USER>/my_ragflow_chunker/test.md"
    
    # 创建分块器
    chunker = RAGFlowChunker(api_key=api_key, base_url=base_url)
    
    # 使用高级分块策略
    try:
        result = chunker.process_file(
            file_path=file_path,
            dataset_id=dataset_id,
            document_id=document_id,
            strategy='advanced',  # 使用高级分块策略
            chunk_token_num=512,  # 更大的分块
            min_chunk_tokens=50,  # 更大的最小分块
            overlap_ratio=0.1,   # 10%重叠
            include_metadata=True,  # 包含元数据
            important_keywords=['AI', 'RAG', '知识库'],  # 重要关键词
            questions=['什么是RAG?', '如何使用RAGFlow?']  # 相关问题
        )
        
        print(f"✅ 高级处理完成: {result}")
        
    except Exception as e:
        print(f"❌ 高级处理失败: {e}")

def example_batch_processing():
    """批量处理示例"""
    print("=" * 60)
    print("📚 批量处理示例")
    print("=" * 60)
    
    # 配置参数
    api_key = "your_api_key_here"
    base_url = "http://localhost:9380"
    dataset_id = "your_dataset_id"
    
    # 要处理的文件列表
    files_to_process = [
        {"file": "doc1.md", "document_id": "doc1_id"},
        {"file": "doc2.md", "document_id": "doc2_id"},
        {"file": "doc3.md", "document_id": "doc3_id"},
    ]
    
    # 创建分块器
    chunker = RAGFlowChunker(api_key=api_key, base_url=base_url)
    
    # 批量处理
    results = []
    for file_info in files_to_process:
        try:
            print(f"📖 处理文件: {file_info['file']}")
            
            result = chunker.process_file(
                file_path=file_info['file'],
                dataset_id=dataset_id,
                document_id=file_info['document_id'],
                strategy='smart',
                chunk_token_num=256,
                min_chunk_tokens=10
            )
            
            results.append({
                'file': file_info['file'],
                'success': result['success'],
                'chunks': result.get('total_chunks', 0),
                'uploaded': result.get('uploaded_chunks', 0)
            })
            
            print(f"✅ {file_info['file']} 处理完成")
            
        except Exception as e:
            print(f"❌ {file_info['file']} 处理失败: {e}")
            results.append({
                'file': file_info['file'],
                'success': False,
                'error': str(e)
            })
    
    # 输出批量处理结果
    print("\n📊 批量处理结果:")
    for result in results:
        if result['success']:
            print(f"  ✅ {result['file']}: {result['uploaded']}/{result['chunks']} 分块上传成功")
        else:
            print(f"  ❌ {result['file']}: {result.get('error', '处理失败')}")

def example_custom_chunking():
    """自定义分块策略示例"""
    print("=" * 60)
    print("🎯 自定义分块策略示例")
    print("=" * 60)
    
    api_key = "your_api_key_here"
    base_url = "http://localhost:9380"
    dataset_id = "your_dataset_id"
    document_id = "your_document_id"
    file_path = "example_document.md"
    
    chunker = RAGFlowChunker(api_key=api_key, base_url=base_url)
    
    # 示例1: 使用正则表达式分块
    try:
        print("🔍 使用正则表达式分块...")
        result = chunker.process_file(
            file_path=file_path,
            dataset_id=dataset_id,
            document_id=document_id,
            strategy='configured',
            sub_strategy='strict_regex',
            regex_pattern=r'^#{1,3}\s+',  # 按1-3级标题分块
            chunk_token_num=512,
            min_chunk_tokens=20
        )
        print(f"✅ 正则分块完成: {result}")
        
    except Exception as e:
        print(f"❌ 正则分块失败: {e}")
    
    # 示例2: 使用基础分块策略
    try:
        print("📝 使用基础分块策略...")
        result = chunker.process_file(
            file_path=file_path,
            dataset_id=dataset_id,
            document_id=document_id,
            strategy='basic',
            delimiter="\n\n",  # 按双换行分块
            chunk_token_num=300,
        )
        print(f"✅ 基础分块完成: {result}")
        
    except Exception as e:
        print(f"❌ 基础分块失败: {e}")

def example_step_by_step():
    """分步骤处理示例"""
    print("=" * 60)
    print("🔧 分步骤处理示例")
    print("=" * 60)
    
    api_key = "your_api_key_here"
    base_url = "http://localhost:9380"
    dataset_id = "your_dataset_id"
    document_id = "your_document_id"
    file_path = "example_document.md"
    
    chunker = RAGFlowChunker(api_key=api_key, base_url=base_url)
    
    try:
        # 步骤1: 读取文件
        print("📖 步骤1: 读取文件")
        content = chunker.read_file(file_path)
        print(f"文件长度: {len(content)} 字符")
        
        # 步骤2: 分块处理
        print("📝 步骤2: 分块处理")
        chunks = chunker.chunk_document(
            content=content,
            strategy='smart',
            chunk_token_num=256,
            min_chunk_tokens=10
        )
        print(f"生成 {len(chunks)} 个分块")
        
        # 步骤3: 预览分块
        print("👀 步骤3: 预览前3个分块")
        for i, chunk in enumerate(chunks[:3], 1):
            print(f"分块 {i} (长度: {len(chunk)} 字符):")
            print(f"  {chunk[:100]}...")
            print()
        
        # 步骤4: 上传分块
        print("🚀 步骤4: 上传分块")
        results = chunker.upload_chunks(
            dataset_id=dataset_id,
            document_id=document_id,
            chunks=chunks,
            important_keywords=['示例', '测试'],
            questions=['这是什么内容?']
        )
        
        print(f"✅ 上传完成: {len(results)} 个分块成功上传")
        
    except Exception as e:
        print(f"❌ 分步处理失败: {e}")

def create_sample_document():
    """创建示例文档"""
    sample_content = """# RAGFlow使用指南

## 简介

RAGFlow是一个开源的RAG（检索增强生成）引擎，基于深度文档理解技术。

### 主要特性

- 深度文档理解
- 多种分块策略
- 灵活的API接口
- 支持多种文档格式

## 安装和配置

### 系统要求

- Python 3.8+
- Docker (可选)
- 至少4GB内存

### 安装步骤

1. 克隆仓库
2. 安装依赖
3. 配置环境变量
4. 启动服务

## API使用

### 创建数据集

使用POST请求创建新的数据集：

```python
import requests

response = requests.post(
    "http://localhost:9380/api/v1/datasets",
    headers={"Authorization": "Bearer your_api_key"},
    json={"name": "my_dataset"}
)
```

### 上传文档

支持多种文档格式的上传。

### 添加分块

可以手动添加分块到指定文档。

## 最佳实践

1. 选择合适的分块策略
2. 调整分块大小
3. 使用关键词标注
4. 定期更新内容

## 常见问题

### Q: 如何选择分块策略？
A: 根据文档类型和使用场景选择。

### Q: 分块大小如何设置？
A: 一般建议256-512 tokens。

## 总结

RAGFlow提供了强大的文档处理能力，适合各种RAG应用场景。
"""
    
    with open("example_document.md", "w", encoding="utf-8") as f:
        f.write(sample_content)
    
    print("✅ 示例文档已创建: example_document.md")

if __name__ == "__main__":
    print("🎯 RAGFlow分块上传工具使用示例")
    print("=" * 80)
    
    # 创建示例文档
    create_sample_document()
    
    print("\n请根据需要修改以下示例中的配置参数:")
    print("- api_key: 您的RAGFlow API密钥")
    print("- base_url: 您的RAGFlow服务器地址")
    print("- dataset_id: 您的数据集ID")
    print("- document_id: 您的文档ID")
    print("- file_path: 您的文档文件路径")
    
    print("\n运行示例:")
    print("1. 基本使用示例")
    print("2. 高级使用示例")
    print("3. 批量处理示例")
    print("4. 自定义分块策略示例")
    print("5. 分步骤处理示例")
    
    choice = input("\n请选择要运行的示例 (1-5, 或按Enter跳过): ").strip()
    
    if choice == "1":
        example_basic_usage()
    elif choice == "2":
        example_advanced_usage()
    elif choice == "3":
        example_batch_processing()
    elif choice == "4":
        example_custom_chunking()
    elif choice == "5":
        example_step_by_step()
    else:
        print("示例代码已准备就绪，请根据需要修改配置参数后运行。")
