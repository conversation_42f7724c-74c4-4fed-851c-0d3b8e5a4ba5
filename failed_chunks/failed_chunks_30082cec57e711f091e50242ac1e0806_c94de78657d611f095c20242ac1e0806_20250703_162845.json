{"timestamp": "2025-07-03T16:28:45.071970", "dataset_id": "30082cec57e711f091e50242ac1e0806", "document_id": "c94de78657d611f095c20242ac1e0806", "total_failed": 5, "failed_chunks": [{"index": 1, "chunk": "# 中国移动北斗智慧监控平台-政企车队用户使用手册 v3.2\n\n中移物联网有限公司2024.06  \n目录", "error": "You don't own the document c94de78657d611f095c20242ac1e0806.", "important_keywords": null, "questions": null, "timestamp": "2025-07-03T16:28:19.285604"}, {"index": 2, "chunk": "# 中国移动北斗智慧监控平台-政企车队用户使用手册 v3.2\n\n- 功能菜单介绍 .  \n1.1. 登录 . .  \n1.2. 工作台 .  \n1.3. 个人中心 . .  \n1.3.1. 基本信息 .  \n1.3.2. 修改密码   \n1.3.3. 手册下载 .  \n1.3.4. 服务条款 . .  \n1.3.5. 隐私政策 .  \n1.3.6. 退出登录 ..  \n1.4. 设置 .  \n1.4.1. 企业资料管理. ..  \n1.4.2. 服务管理. ..  \n1.5. 在线客服 .  \n1.6. 用车管理 . 0  \n1.6.1. 公务用车申请 . 0  \n1.6.2. 公务用车管理. .. 4  \n1.6.3. 私车备案/私车公用管理 . 7  \n1.6.4. 网约车用车管理 . 2  \n1.7. 监控管理 ... 9  \n1.7.1. 车辆监控 .. 9  \n1.7.2. 监控大屏 .. 3  \n1.7.3. 异常监控 . 3  \n1.7.4. 主动安全. .. 7  \n1.7.5. 轨迹分段查询 . 1  \n1.7.6. 分段轨迹导出 .. 3  \n1.7.7. 电子围栏. .. 4  \n1.7.8. 超速设置 . 5  \n1.7.9. 回场查询 .. 6  \n1.7.10. 温湿度监控- .. 6  \n1.8. 费用管理 .. 6  \n1.8.1. 记账本 .. 6  \n1.8.2. 私车公用结算 .. 8  \n1.8.3. 计费规则管理 ... 8  \n1.8.4. 用车预算管理 .. 9  \n1.8.5. 公务用车结算 .. 0  \n1.8.6. 网约车订单结算. .... 1  \n1.9. 车务管理 ... 2  \n1.9.1. 车队管理 .. 2  \n1.9.2. 日常车务申请 ... 8  \n1.9.3. 日常车务管理. . 3  \n1.9.4. 其他车务管理 00  \n1.9.5. 临时用车管理. 01  \n1.9.6. 服务商管理. 02  \n1.9.7. 到期提醒查询 . 03  \n1.9.8. 车辆配备申请 04  \n1.9.9. 车辆配备管理. 11  \n1.10. 统计报表 16  \n1.10.1. 车辆监控统计 16  \n1.10.2. 车辆运营统计 .. 17  \n1.10.3. 异常用车统计 20  \n1.10.4. 费用统计. 21  \n1.10.5. 司机用车统计. 21  \n1.11. 企业管理. . 22  \n1.11.1. 基础信息 22  \n1.11.2. 流程管理. 26  \n1.11.3. 公告管理 27\n- 重点功能介绍. 31  \n2.1. 派单流程. .. 31  \n2.1.1. 使用端说明 . 31  \n2.1.2. 核心流程 . 31  \n2.1.3. 主要功能操作. . 43  \n2.2. 车务申请流程. . 57  \n2.2.1. 使用端说明 . 57  \n2.2.2. 核心流程. . 57  \n2.2.3. 主要功能操作 59", "error": "You don't own the document c94de78657d611f095c20242ac1e0806.", "important_keywords": null, "questions": null, "timestamp": "2025-07-03T16:28:25.630015"}, {"index": 3, "chunk": "# 1. 功能菜单介绍\n\n## 1.1. 登录\n\n访问地址：http://ls.cmobd.com/zqcd/login  \n账号、密码联系管理进行分配。  \n![](http://10.15.1.75:18099/files/images/Userguide/252ff75f5c4ef3842092c1d363fe45320aa62c9471c51cd40141b1ad7da0689a.jpg)", "error": "You don't own the document c94de78657d611f095c20242ac1e0806.", "important_keywords": null, "questions": null, "timestamp": "2025-07-03T16:28:31.981514"}, {"index": 4, "chunk": "## 1.2. 工作台\n\n展示用户能够使用的所有功能菜单。  \n![](http://10.15.1.75:18099/files/images/Userguide/da8ff41db9c87770892b480226e01a5945c23573cd0f8c142ddc5ab8e7f56474.jpg)\n\n## 1.3. 个人中心\n\n### 1.3.1. 基本信息\n\n修改个人基本信息，带\\*为必填信息；包括姓名、电话、出生日期、联系地址、所属区域。  \n![](http://10.15.1.75:18099/files/images/Userguide/9f8b68d758d524a19df8c13b0b9dd599903707db093ebc3140089e7eb04f6b75.jpg)\n\n### 1.3.2. 修改密码\n\n密码修改，需输入原密码、新密码并再次输入新密码。  \n![](http://10.15.1.75:18099/files/images/Userguide/70685e73da8be38e4a35ea203fce0f787597f77e93ae92aec4f9adb2c3100aef.jpg)\n\n### 1.3.3. 手册下载", "error": "You don't own the document c94de78657d611f095c20242ac1e0806.", "important_keywords": null, "questions": null, "timestamp": "2025-07-03T16:28:38.278268"}, {"index": 5, "chunk": "# 点击下载最新操作手册\n\n<html><body><table><tr><td colspan=\"3\">下载</td></tr><tr><td colspan=\"3\">是否下载操作手册到本电脑？</td></tr><tr><td></td><td>取消</td><td>确定</td></tr></table></body></html>  \n### 1.3.4. 服务条款  \n点击查看《中移物联网有限公司服务条款》。  \n### 1.3.5. 隐私政策  \n点击查看《中移物联网有限公司隐私政策》。  \n### 1.3.6. 退出登录  \n点击退出至登录页面。  \n## 1.4. 设置  \n### 1.4.1. 企业资料管理  \n对企业名称和logo 进行更新。  \n![](http://10.15.1.75:18099/files/images/Userguide/b7dd026fa77b3ca263c0b507168d3b701dbff13b1bdddaede34d9094b0340226.jpg)  \n### 1.4.2. 服务管理  \n可查看当前账号启用了哪些基础配置、开通了哪些增值服务，并且可对每项配置和增值服务的状态进行编辑。  \n![](http://10.15.1.75:18099/files/images/Userguide/57593671007b1c7387dcec114c2af2b71ea0b6c73e3459dbda9b01c6a5906ba5.jpg)  \n## 1.5. 在线客服  \n点击页面最右的联系客服按钮，即可接入在线客服服务。  \n![](http://10.15.1.75:18099/files/images/Userguide/527bb4cbb3dd1accca38acd0268f5412a0e3baca7754487f825f3c21a99410c7.jpg)  \n## 1.6. 用车管理  \n### 1.6.1. 公务用车申请  \n用车人或管理员可通过PC 端：用车管理 $>$ 用车申请，发起用车申请。  \n![](http://10.15.1.75:18099/files/images/Userguide/607420c654b8069e6699125e9c7e5ca62a8e676e4647de9a8d65d346aecd805e.jpg)  \n详情：查看此次派单的详情。  \n评价：如果该申请单已完成，且使用的派单流程允许评价，则可评价。  \n撤回：撤回已提交的，处于【待审批】状态的申请。  \n编辑：如果该单是撤回的单，则该单可编辑。  \n完成：针对执行中的订单，选择完成结束本次用车申请流程。  \n确认行程：执行完成操作后，操作项文字由【完成】变为【确认行程】；权限：仅用车人可执行确认行程操作，执行后用车状态确认变为【已确认】。  \n打印：针对【审批通过】、【执行中】、【已完成】、【已拒绝】、【已取消】的订单，具有打印订单的功能。打印信息内容与详情页一致。  \n轨迹：派单状态为执行中、已完成的工单，可查看车辆的轨迹信息；轨迹的查询时间程序限制为 1 周，超过一周未结束用车的行程也仅查询一周内的行程。  \n1、若车派单状态为执行中，则获取当前查询时间与实际开始时间之间的轨迹；2、若派单状态为已完成，则获取实际结束时间与实际开始时间之间的轨迹。权限：管理员和用车人都可以查看轨迹详情。  \n# 新增申请：  \n![](http://10.15.1.75:18099/files/images/Userguide/24737553bddbfedea819b5e5c57e73c865cc3b76367682ab85b6d4723131caca.jpg)  \n用车模式：必填。从流程中选择。企业管理员可选择本企业所有的用车模式，根据用车模式使用频次进行排序，使用最多的排在下拉列表最上面。  \n用车人：可编辑，默认为该账号姓名。  \n用车事由：本次用车事由信息。限定200 字；预置字段，包括：机要通信、商务活动、公务接待、市场营销、工程维护、应急保障、抗灾抢险、跨区域调研、集体通勤用车、综合保障；交互形式：选择器下拉选择，单选操作；光标进入文本编辑框时出现下来选项；也可进行手动输入。  \n备注：文本。限200 个字符。  \n开始时间（计划开始时间）：必填。不可选择历史时间，且计划结束时间必须大于计划开始时间。若工单类型为补单，APP 执行【开始用车】、【结束用车】，或后台执行完成操作，均不记录当前操作时间，仅标记工单状态。  \n结束时间（实际结束时间）：必填。不可选择历史时间，且计划结束时间必须大于计划开始时间。若工单类型为补单，APP 执行【开始用车】、【结束用车】，或后台执行完成操作，均不记录当前操作时间，仅标记工单状态。  \n出发地点：文本。必填。限50 个字符；点击定位logo，弹出地图弹出层（大）；选中地图位置并给出GPS 坐标点的定位位置，如xx 大厦；支持手动填写和编辑。  \n到达地点：文本。必填。限50 个字符；点击定位logo，弹出地图弹出层（大）；选中地图位置并给出GPS 坐标点的定位位置，如xx 大厦；支持手动填写填写和编辑。  \n期待车型：可选，选择期待派车车型。  \n# 补单：  \n用车模式：必填。从流程中选择。企业管理员可选择本企业所有的用车模式，根据用车模式使用频次进行排序，使用最多的排在下拉列表最上面。  \n申请人：不可编辑，该账号的姓名。  \n用车人：可编辑，默认为该账号姓名。  \n用车人电话：可编辑，默认为该账号人员的手机号码。  \n用车事由：本次用车事由信息。限定200 字；预置字段，包括：机要通信、商务活动、公务接待、市场营销、工程维护、应急保障、抗灾抢险、跨区域调研、集体通勤用车、综合保障；交互形式：选择器下拉选择，单选操作；光标进入文本编辑框时出现下来选项；也可进行手动输入。  \n用车人数：本次用车的人员数量。限99 人。  \n出发地点：文本。必填。限50 个字符；点击定位logo，弹出地图弹出层（大）；选中地图位置并给出GPS 坐标点的定位位置，如xx 大厦；支持手动填写和编辑。  \n到达地点：文本。必填。限50 个字符；点击定位logo，弹出地图弹出层（大）；选中地图位置并给出GPS 坐标点的定位位置，如xx 大厦；支持手动填写填写和编辑。  \n开始时间（计划开始时间）：必填。仅可选择过去时间，不可选择现在和将来时间。若工单类型为补单，APP 执行【开始用车】、【结束用车】，或后台执行完成操作，均不记录当前操作时间，仅标记工单状态。  \n结束时间（实际结束时间）：必填。仅可选择过去时间，不可选择现在和将来时间。若工单类型为补单，APP 执行【开始用车】、【结束用车】，或后台执行完成操作，均不记录当前操作时间，仅标记工单状态。  \n备注：文本。限200 个字符。  \n# 用车事由  \n管理员可根据企业需要，预先设置不同用车事由，方便用车申请人在选择用车使用的时候进行快速下拉选择。  \n![](http://10.15.1.75:18099/files/images/Userguide/bc28fbee2b268214c80815aab7383815a8563065f1253e3a31ee5840a8988b3d.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/1882341fa09b54484ca5b5a1198e125d2717af03215ef0ce7a6467f6c3d3b5d7.jpg)  \n用车事由：用车事由，必填；事由描述：备注信息；  \n### 1.6.2. 公务用车管理  \n管理员审批用车，仅管理员有权限查看此菜单。企业管理员可查看企业所有的用车信息，部门管理员可查看部门所有的用车信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/a0b1c2d3e4f56789fedcba0123456789abcdef0123456789fedcba987654321.jpg)\n轨迹：派单状态为执行中、已完成的工单，可查看车辆的轨迹信息；轨迹的查询时间程序限制为 1 周，超过一周未结束用车的行程也仅查询一周内的行程。  \n1、若车派单状态为执行中，则获取当前查询时间与实际开始时间之间的轨迹；  \n2、若派单状态为已完成，则获取实际结束时间与实际开始时间之间的轨迹。权限：管理员和用车人都可以查看轨迹详情。  \n详情：查看此次派单的详情。  \n同意：同意此次申请，有二次确认。同意派单时可添加备注说明，不超过200 个字符；派单流程最后一个审批人进行派车时，可选择所有已绑定的车辆进行派单。若所选择的车辆未匹配司机，则还需继续选择未匹配车辆的司机，该派单流程完成后，该流程对应的车辆与司机可被其他派单流程选择，最后一个审批人在派车和改派时均需要添加备注信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/425d2e1d852c4da5087f1d0bbd7ddc760cd69b41651f4a2efb4ad5b6ec0a4280.jpg)  \n改派：改派其他车辆。  \n拒绝：拒绝此次申请，并填写拒绝原因。针对已审批通过的订单也可拒绝，拒绝时需要注明理由。  \n开始：针对审批通过的用车申请，有开始操作；点击【开始】按钮后，按钮文字变成完成； APP 操作【开始用车】按钮后，企业端电脑后台的此处按钮文字变为完成；注意：  \n只有最后一个管理员才可以进行 【开始】、 【完成】 操作。  \n完成：针对执行中的工单，有完成操作。  \n“确认本次用车已完成” “确定” “取消”  \n改派：针对审批通过的订单，可选择改派司机和车辆。  \n打印：针对【审批通过】、【执行中】、【已完成】、【已拒绝】、【已取消】的订单，具有打印订单的功能。  \n流程说明：  \n1. 相同节点存在多个审批人时，则采用竞争机制，谁先审批则当前审批节点记录相应的审批人，其他具有审批权限的管理员有查看“详情”操作。  \n2. 多级审批人时：前一审批人通过，后一审批人才可操作。  \n3. 按照审批流程，在需要某一审批人审批时，向该审批人发送短信和信息。多人具有审批权限时，则向多人发送提醒短信。  \n4. 审批通过后，短信和消息通知用车人和司机；审批被拒绝，则通知用车人。  \n5. 相同节点存在多个审批人时，针对已审批通过的订单，“拒绝”“改派”操作权限仅当时审批人具有，其他审批人仅有查看的权限。  \n6. 企业管理员可查看企业所有的派单信息。  \n7. 部门管理员派单列表中包括：需要审批的派单及部门及下级部门的派单信息。 $\\textcircled{1}$ 、当查询条件为管理员所在部门及下级部门时：列表展示所在部门及下级部门的所有派单信息；  \n$\\textcircled{2}$ 、当查询条件为申请人父级部门时：列表展示需要当前管理员审批的派单和自己部门及下级部门派单信息；  \n$\\textcircled{3}$ 、当查询条件为其他平级部门时，列表展示需要当前管理员审批的派单。  \n### 1.6.3. 私车备案/私车公用管理  \n# 私车公用流程介绍：  \n首次使用私车公用的车辆需要通过私车信息备案，上传车辆行驶证、保险、用车人驾驶证等信息，管理员审批通过和绑定设备后，方可申请私车公用出行。管理员审批通过私车公用单，用车人开始执行出车任务，用车结束后，系统会自动根据产生的里程数生成私车公用费用结算单。  \n管理员在审批私车公用单子时，可以设置该趟行程的不合格里程比例，如果用车结束后产生的里程数据超出该比例，会产生不合格用车单，单子会重新流转至管理员处进行审核，由管理员定义该用车单是否为合格用车单，不合格用车单不会纳入私车公用结算单中。  \n# 主要功能说明：  \n（1）私车备案申请（用车人）功能导航APP-私车备案申请  \n# 使用步骤  \n车牌号：输入申请私车备案的车辆牌号，如渝AFC888；  \n车主与本人的关系：输入10 个字符以内，如本人、哥哥的车等；  \n购买时间：选择车辆购买时间，选择年月日；  \n品牌型号：输入备案车辆的品牌型号，如奥迪Q7；  \n准驾车型：选择车辆的准驾车型；  \n行驶证姓名：车辆行驶证上的姓名；  \n车辆识别代码：输入车辆支持识别码，支持17 位英文和数字组合；  \n申请人驾驶证号：输入申请人的驾驶证号，支持18 位数字；  \n驾驶证领证时间：选择驾驶证领证时间；  \n保险到期时间：选择保险到期时间；  \n三责险保额：填写三责险保额，支持汉字和数字组合，20 字以内；  \n座位险保额：填写座位险保额，支持汉字和数字组合，20 字以内；  \n申请事由：系统展示，无需填写；  \n附件：需上传身份证、行驶证、驾驶证、保险单和安全承诺说明书图片附件，每类至少上传一张图片。  \n![](http://10.15.1.75:18099/files/images/Userguide/42e5b607373fb5f6a53e14c4238af87d7dc4cb6a9a38e421f88d93a36925e300.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/58dc50948f00f6e513d31689a4a1493de21defa070c06eddea9aca8c2140e73c.jpg)  \n# （2）私车备案管理（管理员）  \n功能导航web-私车公用-私车备案管理APP-私车公用-私车备案管理  \n# Web 使用步骤  \n私车备案管理页展示用车人备案申请的信息，其中【私车备案管理】页面展示所有状态的备案单子，【待审批】、【已完成】和【已退回】页面展示对应状态的单子。�  点击【详情】，管理员可查看私车备案申请详情。  \n点击【同意】，同意该用车人的备案申请，备注为选填项。同意的备案申请车辆支持在私车公用申请时进行选择。  \n点击【退回】，退回该用车人的备案申请，退回原因为必填项，退回的备案申请，用车人可以重新修改后再提交。  \n![](http://10.15.1.75:18099/files/images/Userguide/2cbc5275714bc52f03f4d6528433620a85ae916d47d189f51d2ce418022c1e70.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/162043b2b6f1d17ecaf7223d8271a696c9e9f6e1c77d5b6c8568c7e1f8a57382.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/4cf25b8c30091210ad92ac417fd0f9770b6c425ba996eb7d707b4c3f02d06288.jpg)  \n# APP 使用步骤  \n私车备案管理列表页面每条申请单卡片上有【同意】和【退回】按钮，点击卡片进入备案申请详情信息页面。  \n点击列表页卡片或详情信息页【同意】后，页面直接跳转至列表首页，申请单子状态变为”已完成“，同意的备案申请车辆支持在私车公用申请时进行选择。  \n点击列表页卡片或详情信息页【退回】后，退回该用车人的备案申请，退回理由为必填项，提交退回后，用车人可以重新修改后再提交。  \n![](http://10.15.1.75:18099/files/images/Userguide/5e9677e0294be91dd1e3e06faa79ce9192ed055272176a9bea3e5367bc684f48.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/2e34790914e3c92890b5f223b7b3d6064105169383511337611799fafb1a3bff.jpg)  \n# （3）车辆信息创建和设备绑定（管理员）  \n# 功能导航  \nweb-车辆管理-车辆信息-【新增】  \n# 使用步骤  \n$\\textcircled{1}$ 创建车辆信息  \n车辆使用场景选择：标红色星号选项为必填项，”车辆使用场景选择“私车公用”，通过私车备案审批的车辆（审批状态为”已完成“）支持新增、批量新增、编辑和批量编辑车辆使用场景为私车公用，否则提示”该车辆未通过私车备案申请，无法添加。  \n费用预估信息填写：此处选择的是“web-车辆管理-企业车型简称管理”中的计费方式，此选项与私车公用结算费用计算方式关联。（该项若为空，则无法提交私车公用申请单）  \n# 新增车辆  \n# ■基本信息  \n![](http://10.15.1.75:18099/files/images/Userguide/1722d83d9f34f3c139dd575e28c60d5f757192554df4a917f7b767f5b3a17fd7.jpg)  \n$\\textcircled{2}$ 绑定设备  \n点击【车辆信息】-【更多操作】-【绑定】，输入设备信息完成车辆和设备的绑定。  \n![](http://10.15.1.75:18099/files/images/Userguide/272988e91dab6376fbae72139db92d779393221340455aa6c6b06b6b238ede35.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/96072f85539650bf5e2f5888f5215c3cd3aaa1189a69688ce63d06db86e56af9.jpg)  \n# （4）私车公用申请（用车人）  \n# 功能导航  \nAPP-私车公用申请  \n# 使用步骤  \n点击私车公用申请首页右上角 $[ + ]$ ，进入新增私车公用申请页面，输入以下用车信息：  \n用车人： 系统默认为本人，无需填写；  \n同行人：输入同行人名称，选填；  \n车牌号：选择已备案的车牌号用于公务出行；  \n用车事由：选择出行的用车事由；  \n用车模式：选择默认模式，即默认该用车人所属部门管理员进行审批；  \n计划出发点：支持手动在地图上选择出发地点；  \n计划结束点：支持手动在地图上选择结束地点；  \n计划途径点：最多支持3 个途径点选择，选填；  \n说明：支持输入50 字以内的文字说明，选填。  \n点击【继续】后，页面展示预估里程、时长、费用和轨迹，点击【轨迹】，进入规划轨迹详情页面（目前平台按里程最少轨迹进行智能规划）。  \n点击【申请用车】，提交私车公用申请单。  \n![](http://10.15.1.75:18099/files/images/Userguide/013329b2b33baf4905503920d284b1fa121e6ad4d6c78947e7c54670cfd659d4.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/824ba53e10151932408287699b0948ec09cca7c02e0e9c8a01115034ca2b5551.jpg)  \n# （5）私车公用管理（管理员）  \n功能导航web-私车公用-私车公用管理APP-私车公用管理  \n# Web 使用步骤  \n私车公用管理页展示用车人私车公用申请的信息，其中【私车公用管理】页面展示所有状态的申请单子，【待审批】、【待出车】、【出车中】、【不合格用车待审批】、【已完成】和【已拒绝】页面展示对应状态的单子。  \n点击【详情】，管理员可查看私车公用申请详情。  \n点击【同意】，同意该用车人的私车公用申请，备注为选填项。“请设置不合格用车的里程偏差比例（单位:%）”需填写判定为不合格用车的偏差比例，默认配置为 $5 0 \\%$ 。不合格用车定义为超过里程偏差值的用车单，里程偏差值定义为 $50 \\%$ ，则实际里程小于 $50 \\%$ 预估里程和实际里程大于 $50 \\%$ 预估里程均为不合格用车。同意的申请后，用车人可以在APP 上点击开始用车。  \n点击【拒绝】，退回该用车人的私车公用申请，拒绝原因为必填项，退回的私车公用申请，用车人可以重新修改后再提交。  \n![](http://10.15.1.75:18099/files/images/Userguide/609324e65a0106589ba4a95e7bdf8ecc2baea5132b87a528b166eaab4fb96bd8.jpg)  \n<html><body><table><tr><td>工单号</td><td>2312060001</td><td>用车模式默认模式</td><td></td></tr><tr><td>申请时间</td><td>2023-12-06 14:48:53</td><td>用车人 李逍遥</td><td></td></tr><tr><td>用车人部门</td><td>测试部门</td><td>同行人 高启盛</td><td></td></tr><tr><td>车牌号</td><td>渝AFC888</td><td></td><td></td></tr><tr><td>计划开始时间</td><td>2023-12-06 15:30</td><td>计划结束时间2023-12-0616:59</td><td></td></tr><tr><td></td><td>计划出发地点北京市东城区东华门街道正义路12号中华人民共和国公安部</td><td></td><td></td></tr><tr><td></td><td>计划到达地点北京市公安局行政复议办公室接待室</td><td></td><td></td></tr><tr><td>用车事由</td><td>去白金瀚接领导</td><td>计费方式里程</td><td></td></tr><tr><td>说明</td><td>接领导</td><td></td><td>89.00元</td></tr><tr><td>预估里程</td><td>3.4km</td><td>预估费用</td><td></td></tr><tr><td>预估时长</td><td>13分</td><td></td><td></td></tr><tr><td>工单状态待审批</td><td></td><td>accccccccccww-待审批</td><td>工单流程李逍遥,呵呵哒，qaqa,iuu,cece, bm,titi，测试部门管理员三，测试 部门管理员二，测试部门管理 员—,qqqqqqqqqqaaaaaaa</td></tr><tr><td>NO.</td><td>操作人 操作时间</td><td>详情</td><td>工单状态 说明</td></tr><tr><td>1</td><td>李逍遥 2023-12-06 14:48:53</td><td>用车人李逍遥创建私 车公用申请</td><td>待审批</td></tr></table></body></html>  \n# 同意用车  \n![](http://10.15.1.75:18099/files/images/Userguide/cfabc4621ab5bdab99277181bd591844c2e4fdd81a57e416556a2922c6a52095.jpg)  \n# 拒绝用车  \n是否拒绝该申请？如果拒绝，请填写原因并确定：  \n![](http://10.15.1.75:18099/files/images/Userguide/0ef51a1303cc5504b1ba05f1207010bcbe31d4a5ad324fd4a355983a0a07afa6.jpg)  \n# APP 使用步骤  \n私车公用管理列表页面每条申请单卡片上有【同意】和【拒绝】按钮，点击卡片进入私车公用申请详情信息页面。  \n点击列表页卡片或详情信息页【同意】后，进入同意申请页面，需填写不合格用车里程偏差比例，备注说明为选填，点击【确定】后，申请单子状态变为”待出车“。  \n点击列表页卡片或详情信息页【拒绝】后，拒绝该用车人的申请，拒绝理由为必填项，提交拒绝后，用车人可以重新修改后再提交。  \n![](http://10.15.1.75:18099/files/images/Userguide/5806dc27877dfe19c6a69efb17c07d3cb2393c656d772c9f5cb9e11b7b368325.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/12a03629cb51e1b7111da5d5cc509eddc0999abe74e4fb0ae51efeb1a97ea4cb.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/7ca580c3927ba0aa7a34294dbf36d707543d080b714262aa454b1e3b78c95cfd.jpg)  \n# （6）执行用车任务：  \n# 功能导航  \nAPP-私车公用申请  \n# APP 使用步骤  \nAPP-私车公用申请列表页面或对应申请单卡片详情页面，点击【开始用车】，若当时能获得位置报文，则弹窗提示”要开始用车吗“，点击确定后，用车人可以执行用车任务；若点击【开始用车】后当时能没获得位置报文，则弹窗提示”当前没收到定位信息，请稍等一会再试“。  \n执行任务结束后，APP-私车公用申请列表页面或对应申请单卡片详情页面，点击【结束用车】，若当时能获得位置报文，则弹窗提示”要结束用车吗“，点击确定后，用车人结束用车任务；若点击【结束用车】后当时能没获得位置报文，则弹窗提示”当前没收到定位信息，请稍等一会再试“。  \n结束用车后，若该用车单产生的实际里程在不合格用车偏离比例范围之内，则用车单状态变成“已完成”；若里程在不合格用车偏离比例范围之外，则用车单状态变成“不合格用车待审批”，该用车单会再次流转至管理员处进行审批，由管理员定义该用车最终是否为合格用车。  \n![](http://10.15.1.75:18099/files/images/Userguide/6122c04c6350e862b4e137e399683dc3713e6e0dcfdae37c3341a42bd9a5f218.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/0c1217f0c14a3db66d5e943d22631fdf7899689b1b5381ba64b1d7ae2e393025.jpg)  \n# （7）管理员审批不合格用车  \n# 产生条件：  \n用车人结束用车后产生的实际里程在不合格用车里程偏离范围之外，该用车单会再次流转至管理员处进行审批，由管理员定义该用车最终是否为合格用车。  \n# web 使用步骤：  \n私车公用管理-不合格用车待审批页面，支持管理员查看系统判定的不合格用车单的详情和轨迹信息。  \n点击【操作】-【不合格用车审批】，“不合格用车待审批”弹窗页面展示该用车单的里程详情，管理员可以在“请审批该工单是否为合格用车”选项中选择“合格用车”或“不合格用车”，合格用车的工单会纳入私车公用结算单管理。点击“实际里程”右边的【刷新里程】按钮，可以刷新成最新里程，弥补 gps 报文补报行为的里程数据。点击【确定】提交后，工单状态变为“已完成”。  \n![](http://10.15.1.75:18099/files/images/Userguide/b9557dbacbfcf240abebcf09693501e931367308cb475355d565f608fbae5da7.jpg)  \n用车详情  \n![](http://10.15.1.75:18099/files/images/Userguide/6f5e4d3c2b1a09f8e7d6c5b4a39281f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c5.jpg)\n# 不合格用车待审批  \n<html><body><table><tr><td colspan=\"4\">拍用+付中站 请审批该工单是否为合格用车</td></tr><tr><td colspan=\"4\">请选择</td></tr><tr><td colspan=\"4\">渝AFC888 用车人 李逍遥</td></tr><tr><td>车牌号 出发地点</td><td>北京市东城区东华门街道正义</td><td>结束地点</td><td>北京市公安局行政复议办公室</td></tr><tr><td></td><td>路12号中华人民共和国公安部</td><td></td><td>接待室</td></tr><tr><td>实际开始时间</td><td>2023-12-06 16:11:32</td><td>实际结束时间</td><td>2023-12-06 16:13:44</td></tr><tr><td>实际里程</td><td>13.4km 刷新里程</td><td>里程偏差值</td><td>+10km</td></tr><tr><td>用车时长</td><td>2分</td><td>用车金额</td><td>2.0元</td></tr><tr><td colspan=\"4\">取消</td></tr></table></body></html>  \n# APP 使用步骤  \n私车公用管理页面，点击用车单卡片，支持管理员查看系统判定的不合格用车单的详情和轨迹信息。  \n点击用车单卡片或者用车详情页面的【不合格审批】按钮，进入“不合格审批”页面，管理员可以在“请审批该工单是否为合格用车”选项下选择“合格用车”或“不合格用车”，点击【确定】提交后，工单状态变为“已完成”，合格用车的工单会纳入私车公用结算单管理。点击详情页面实际里程右边的【刷新】按钮，可以刷新成最新里程，弥补 gps 报文补报行为的里程数据。  \n![](http://10.15.1.75:18099/files/images/Userguide/233d79a167d0e3876523b7172241af77108a38950fd8e30b31ac4aca844b6e96.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/e66a8160ff327671c90f6ecb0bf05d744364ecb653da4697d2ac0176386161de.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/80b1c3ba923c838e86dfd45e1b899d3957c8c56099529c19128c4b47cb5994dd.jpg)  \n# （8）私车公用结算（用车人&管理员）：  \n# 功能导航  \nweb-私车公用-私车公用结算（合格用车的用车单会纳入私车公用结算页面）  \n# 使用步骤  \n私车公用结算页面支持查看已完成的合格用车单详情信息。  \n点击【操作】-【详情】，可以查看合格用车单详情信息。  \n点击【操作】-【打印】，可以用车单对应的”私车公用结算单“的详情信息，包括申请详情、审批详情和行程详情等内容，点击【确定】进入”打印预览“页面，支持本地打印。  \n![](http://10.15.1.75:18099/files/images/Userguide/eb99e0b4ac1a7ed4ed05ae3546ce88c0c865552372317258878f9fc9d4b0d208.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/cafebabe0deadbeef1234567890abcdeffedcba0987654321c0ffee0facade1.jpg)\n![](http://10.15.1.75:18099/files/images/Userguide/83d9a2e7c4b1f5086e2a9d3b0c5f7e1a492b6c0d8f3e5a1b7c9d2e4f6a8b0c1.jpg)\n![](http://10.15.1.75:18099/files/images/Userguide/f8014b6cbf75aaac153984543bd50f3c24b4bd9efa9d7b92551432116d2ea57a.jpg)  \n# （9）其他说明  \n# 管理员权限说明  \n管理员可以查看和审批自己审批的用车单信息，也可以查看下级部门的用车单信息。  \n# 审批模式说明  \n审批私车备案、私车公用和审批不合格用车单的管理员默认配置为该用车人直属部门的管理员，暂时不支持自定义，如有需要可联系售前同事进行定制咨询。  \n# 车辆计费方式设置  \nweb-车辆管理-企业车型简称管理，点击【新增】支持车辆简称名称和计费方式。  \n# 配置好后的计费方式，支持在车辆信息详情中与车辆进行匹配。  \n![](http://10.15.1.75:18099/files/images/Userguide/e7fde2f2615906311699d1f5039ed469ce5e851c0881275af3de3b1f7639f0df.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/e40ff8c744515ba605be29f6c52865a9dbdd5f73bc5e7a357ddec60fbb7826c9.jpg)  \n<html><body><table><tr><td>计费方式</td><td>里程单价（元</td></tr><tr><td>里程时长计费</td><td>5</td></tr><tr><td>里程计费</td><td>10 </td></tr><tr><td>里程计费</td><td>10</td></tr><tr><td>里程台班计费</td><td>10 </td></tr><tr><td>里程台班计费</td><td>10 </td></tr><tr><td>里程计费</td><td>10 </td></tr><tr><td>里程台班计费</td><td>20 </td></tr><tr><td>里程台班计费</td><td>50</td></tr><tr><td>里程计费</td><td>3.2</td></tr><tr><td>里程计费</td><td>5</td></tr></table></body></html>  \n<html><body><table><tr><td colspan=\"2\">X 新增车型简称</td></tr><tr><td colspan=\"2\">*车型简称</td></tr><tr><td colspan=\"2\">1</td></tr><tr><td colspan=\"2\">*计费方式 里*里程单价+行驶时长*时间计费单价 里程台班计费</td></tr><tr><td colspan=\"2\">实际行驶里程*里程单价 实际行驶里程*里程单价</td></tr><tr><td colspan=\"2\">*里程单价 请输入公里计费金额 元 里程*里程单价+台班*台班单价</td></tr><tr><td colspan=\"2\">里程结算单价</td></tr><tr><td colspan=\"2\">里程*里程单价+台班*台班单价 0 元</td></tr><tr><td colspan=\"2\">（实际行驶里程－起步里程）*里程单价 ★台班单价</td></tr><tr><td colspan=\"2\">里程*里程单价+台班*台班单价 请输入台班计费金额 元 里程*里程单价+台班*台班单价</td></tr><tr><td colspan=\"2\">台班结算单价</td></tr><tr><td colspan=\"2\">实际行驶里程*里程单价 0 元</td></tr><tr><td colspan=\"2\">（实际行驶里程－起步里程）*里程单价 说明：费用=里程*里程单价+台班*台班单价</td></tr><tr><td colspan=\"2\">取消 确定</td></tr></table></body></html>  \n# 短信通知和 APP 首页 push 环节  \n$\\textcircled{1}$ 私车公用申请后通知管理员审批。  \n$\\textcircled{2}$ 私车公用审批被拒绝提示用车人。  \n$\\textcircled{3}$ 私车公用审批通过提示用车人。  \n$\\textcircled{4}$ 执行中的工单已超出计划时间2 小时，提醒用车人及时结束用车。  \n$\\textcircled{5}$ 通知管理员审批不合格用车单。  \n$\\textcircled{6}$ 不合格用车审批结束提示用车人。  \n### 1.6.4. 网约车用车管理  \n使用端说明：  \n（1）支持用车人、管理员在 APP 端或者web 端进行网约车用车申请、叫车操作、车费支付和查看行程操作；（2）支持管理员在APP 端或者 web 端进行用车审批和查看部门历史用车工单。（3）支持管理员在web 端进行网约车历史订单费用明细查询。  \n# 核心流程：  \n（1）主要操作流程：用车人提交网约车用车申请后，按照该订单指定的审批流程进行管理员审批，审批通过后，订单状态变更为待叫车状态。用车人可通过手机端点击对应的工单进入详情页面，点击开始叫车按钮进行打车操作。行程结束后由用车人进行行程确认和支付的操作，操作完成后工单状态自动变更为已完成。  \n（2）工单状态说明：  \n$\\textcircled{1}$ 待审批：用车人提交用车申请以后工单状态为“待审批”；$\\textcircled{2}$ 待叫车：审批流程上的管理员全部审批后，工单状态为“已审批”；$\\textcircled{3}$ 出车中：当用车人在工单详情页面点击“开始叫车”之后，工单状态变为“出车中”。$\\textcircled{4}$ 已完成：当用车人完成行程费用支付后，工单状态自动变为“已完成”。$\\textcircled{5}$ 已拒绝：管理员审批用车单时拒绝了该单子，工单状态变为”已拒绝“，”已拒绝“的单子用车人可重新提交；$\\textcircled{6}$ 已取消：用车人提交用车申请以后，在管理员审批之前（工单状态为”待审批“）可以撤回该申请，撤回后工单状态变为”已取消“，用车人可以重新编辑后再次提交；  \n# 主要功能操作  \n# App 端：  \n# 用车人角色  \n点击“网约车用车申请”进入我的申请页面，点击右上角 $\" + \"$ ”符号新增一条网约车用车申请。在新建申请界面填写完用车人、开始和结束时间、行程详细信息之后，点击右下角“申请用车”将本次申请提交到审批流程。  \n![](http://10.15.1.75:18099/files/images/Userguide/fe84e89c1813e4a0f8f72bb7d09ec2ddaaed56d98bb598c4aef776052338c705.jpg)  \n手机端-新增网约车用车申请  \n![](http://10.15.1.75:18099/files/images/Userguide/563056129247a9161b3a962ce9d106cc7fe723d5bf5407334dde5659f8d6ace4.jpg)  \n工单审批完成之后，用车人在工单详情页面点击“开车叫车”按钮进入打车界面，同时工单状态变更为“出车中”。在打车界面用车人确认行程起始点（实际起始点在审批单填写起始点 500 米范围内即可）、选择好车型和网约车运营商之后点击“立即打车”，即可正式进入打车流程。  \n![](http://10.15.1.75:18099/files/images/Userguide/52ab4085ad2da6641c69a18f814e3d9b838dffd79c84f737095039fe0e487d36.jpg)  \n手机端-叫车操作流程  \n当工单状态为“出车中”时，用车人可在工单详情界面点击“查看”按钮查看行驶过程中的状态信息，行程结束后，用车人需在打车界面手动确认费用信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/84a81a6c9d32479b5ce7e2ef23c51e988bf6aa25e97f07732744f1e84479c37a.jpg)  \n# 手机端-行程明细  \n费用支付完成后，工单状态变更为“已完成”状态。用车人在工单完成之后依然可以通过查看按钮查看行程详细信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/ae62e32b09ae42bcb632feaedd2efc69f3882a222dc42ee12aef01d239b8d888.jpg)  \n# 管理员角色  \n点击“工作台”-“网约车用车”-“网约车用车管理”进入网约车用车审批页面，可查看待审批和审批过的用车工单，对于待审批的工单可在查询页面进行同意和拒绝操作，也可在详情页面操作。工单审批完成之后，工单状态变更为“待叫车”。  \n![](http://10.15.1.75:18099/files/images/Userguide/d159f6232365c80bbca4e329b36c68a42710803b57505d8514807edb49e462ad.jpg)  \n# Web 端：  \n# 管理员角色  \n管理员可在 web 端“用车管理”-“网约车用车管理”对网约车工单进行审批操作以及查看详情等操作，审批逻辑和app 端一致。  \n![](http://10.15.1.75:18099/files/images/Userguide/f0ed06804becf74b82438350739485f8ee14418274014dcd6fbfd58f79b3a229.jpg)  \nweb 端-网约车用车管理总览页面  \n![](http://10.15.1.75:18099/files/images/Userguide/eeeeb4c7a1a4334a197544ee7c19c5f3b086bf16f92b0b42f451bf89f48684bf.jpg)  \nweb 端-网约车工单详情页面  \n点击“费用管理”-“网约车订单结算”，可查看部门以下所有订单的费用信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/92375b51a726e875b5b2c10e2ef78c58b56ac6ca796bd2c5eb7ef7b30f274978.jpg)  \nweb 端-网约车订单结算界面  \n## 1.7. 监控管理  \n### 1.7.1. 车辆监控  \n#### 1.7.1.1. 地图模式  \n进入地图模式页面，地图模式主页面分为部门列表、车辆列表、地图部分，其中车辆列表提供检索功能。  \n![](http://10.15.1.75:18099/files/images/Userguide/61dce8bccbdf233502d7d3be737407ffe097728d7330cc5f9b4fd3037272319b.jpg)  \n地图模式 历史轨迹  \n# 车辆列表  \n车辆列表显示的所勾选的部门中所有的车辆展现。同时支持检索所选的部门车辆，检索支持模糊搜索。展现车辆信息有车牌号和车辆在线状态（在线静止、在线运动、离线）。点击任意车辆，可查看车辆在右侧地图位置以及车辆的相关信息，如下图所示。  \n![](http://10.15.1.75:18099/files/images/Userguide/799d8deac76a1f23d4bf814ac210aa17a5f9e6ab161b068edebac2daea5de3bd.jpg)  \n弹出页面主要是展现车辆的车牌号、所在位置、司机姓名、司机手机、上报时间、所示部门、设备类型等信息。并查看车辆相关信息：  \na) 详情：点击后查看单车的档案和年检、保险信息（如果在新建车辆时已填写）、实时追踪。  \nb) 轨迹：点击后默认查看当天的轨迹。c) 追踪：点击后弹出新页面对车辆进行实时跟踪。d) 栅栏：点击后新建栅栏，对象默认为该车辆。e) 历史停靠：当用户点击此按钮时，在地图上显示选中车辆当天的停靠位置，进入历史停靠位置查询后，用户可通过上方的时间区间选择，查询车辆在历史时间停靠位置。  \nf) 拍摄：若设备为“和云镜”可进行“远程拍摄”按钮，其他设备不支持“远程拍摄”。  \n![](http://10.15.1.75:18099/files/images/Userguide/3862b4c515e1d05e6703c38284c0bf3f816c565f9394fa31fe2c399edacfc4d0.jpg)  \n# 3) 设置  \n地图右边如下所示，页面设置按钮。可根据车辆在线状态（静止、运动、离线）进行选择显示的车辆。可设置页面刷新频率，不少于30 秒。  \n![](http://10.15.1.75:18099/files/images/Userguide/4e1ea45ee110210f751cfb733162a54d3d98c224ada8ed0f4fc2c154a3bdf332.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/0b17d660437d28526d9171192d748beb70a7f6fc7f44ec494da448ad9f0b5451.jpg)  \n#### 1.7.1.2. 历史轨迹  \n车辆历史轨迹查询，可以查询指定车辆，在一段时间范围内的轨迹，增加自定义数据过滤条件，滤除无效数据。  \n![](http://10.15.1.75:18099/files/images/Userguide/abb9133b05e2d8346c4328ade9c1f8372dd4d8d3caf9a31beb828a05740a4fd1.jpg)  \n### 1.7.2. 监控大屏  \n车队运营监控大屏，大屏展示企业车辆信息、车辆行驶里程分析、昨日行驶时长分布、近30 日实际出车&派单趋势、近 30 日行驶里程&行驶时长、今日报警分布、近 30 日报警趋势数据图，并在大屏中央展示车辆分布地图。  \n![](http://10.15.1.75:18099/files/images/Userguide/116c5da59af0e8312ed9cad5b3bd8bbc1ee9fce17e119df13710e1b4e0848cd0.jpg)  \n### 1.7.3. 异常监控  \n#### 1.7.3.1. 异常用车  \n主要异常用车类型有越界、拆除（OBD）、违章、超速。  \n![](http://10.15.1.75:18099/files/images/Userguide/7d0f5877d1184be4e39db2c8b3f83c45b54093d6a70bc2677982e7ff73ccc01a.jpg)  \n# 1) 搜索  \n可通过时间段，车牌号，所属部门，违规类型和是否已处理查询历史所有异常用车记录。  \n# 2) 历史记录  \n如未进行查询，则按照时间倒序分页显示历史所有异常用车记录。如进行查询，则按照时间倒序分页显示所有查询结果。  \n# 3) 处理  \n点击操作页面后的待处理，进行异常用车的处理，如已处理，也可点击已处理对处理进行编辑，页面如下图所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/9eee1bdaedf051b104327f7b19e54b56fa59acbef05debaef346b8e8e6cdd553.jpg)  \n特别说明：  \n当违规类型筛选为超速时，显示超速相关详情，如下图所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/09674f5721485c2b62cb3b197741abd8d4d6cef4aaa4d457307cab844883d8dc.jpg)  \n超速列表字段：车牌号、所属部门、司机姓名、处理时间、违规类型、处理状态、超速比例、超速时长、发生时间、结束时间、发生位置、结束位置、最高速度、最低速度、平均速度、超速限制。  \n#### 1.7.3.2. 故障维修  \n主要故障类型有故障（OBD）、低电压、存储单元故障、碰撞（OBD），表示目前仅 OBD 终端设备提供故障告警功能。  \n![](http://10.15.1.75:18099/files/images/Userguide/b9cc091498289ee0f922e25d21a5553a51ad1343e615f86c833a7dfc73caf4cf.jpg)  \n1) 搜索查询 可通过时间段，车牌号，所属部门，车辆别名、报警类型和是否已处理查询历史 所有故障维修记录  \n2) 历史记录 如未进行查询，则按照时间倒序分页显示历史所有故障维修记录。如进行查询，则 按照时间倒序分页显示所有查询结果。  \n3) 处理 点击操作页面后的待处理，进行故障维修的处理，如已处理，也可点击已处理对处 理进行编辑，页面如下图所示： 已处理的故障维修中的金额会计入当日的用车费用，并表明费用原因。  \n#### 1.7.3.3. 音视频告警  \n车辆发生拍摄视频、碰撞视频类型后，将产生视频告警。  \n![](http://10.15.1.75:18099/files/images/Userguide/7f5269525d320699d71b5433656d587e0a2ea3a9e27c82fcc35a404a3f517a58.jpg)  \n搜索查询 可通过时间段，车牌号，所属部门，视频类型进行查询  \n可进行播放、下载、删除操作  \n### 1.7.4. 主动安全  \n#### 1.7.4.1. 风险看台  \n主要查看风险车辆在地图上的分布，风险包含高风险、中风险和低风险。  \n![](http://10.15.1.75:18099/files/images/Userguide/968e44c7413d19c11b48d4d018007800b964e0d0fa2b7df97c55afcd232d6174.jpg)  \n1) 搜索查询 可通过车牌号或驾驶员搜索对应风险车辆。  \n#### 1.7.4.2. 主动安全监控大屏  \n主要查看车辆主动安全监控视频。  \n![](http://10.15.1.75:18099/files/images/Userguide/a5dbdb1969e0687bb51c2df48210b92f027a495fb58aec8620f970ae45144bda.jpg)  \n#### 1.7.4.3. 报警明细  \n主要查看主动安全报警明细，报警类型分为高级驾驶辅助报警和驾驶员监控状态报警两大类。分页展示查询到的报警明细信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/2c872a2c9a88ff29abb1494016530686be481f7cb43cef9efa4048abb72db903.jpg)  \n1) 搜索查询 可通过时间段，车牌号，所属部门，司机姓名、报警等级、报警类型和是否已处理查询历史 所有报警记录。  \n2) 历史记录 如未进行查询，则按照时间倒序分页显示历史所有报警记录。如进行查询，则按照时间倒序分页显示所有查询结果。  \n3）轨迹查看 点击后可以查看报警时的车辆轨迹。  \n4）处理 点击操作页面后的处理，进行报警的处理，如已处理，也可点击已处理对处理进行编辑。  \n5）导出 将查询出的报警明细导出成 excel 文件保存。  \n#### 1.7.4.4. 风险记录  \n主要查看风险记录明细，分页展示查询到的风险记录明细信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/d44135895bc1e090fdfdbb97bc59daa1b41ed06ab009e6ce621144d150bde696.jpg)  \n1) 搜索查询 可通过时间段，车牌号码，所属部门，司机姓名、风险等级查询历史所有风险记录。  \n2) 历史记录 如未进行查询，则按照时间倒序分页显示历史所有风险记录。如进行查询，则按照时间倒序分页显示所有查询结果。  \n#### 1.7.4.5. 附件上传策略配置  \n主要查看部门附件上传策略配置信息，分页展示查询到的附件上传策略配置明细信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/5e177f2728024b87689600841e9f4c5dfc456b462b4ab5b6dcd4a9e33f84c660.jpg)  \n1) 搜索查询 可通过部门查询所有附件上传策略配置信息记录。  \n2）编辑 点击后对部门的附件上传策略配置信息进行调整。  \n#### 1.7.4.6. 风险级别配置  \n主要查看部门风险级别配置信息，分页展示查询到的风险级别配置明细信息。  \n![](http://10.15.1.75:18099/files/images/Userguide/e37c21d63494222e3a49449f4585b2458b887c8b8845ac5fd5e09402497468cc.jpg)  \n1) 搜索查询 可通过部门查询所有风险级别配置信息记录。  \n2）编辑 点击后对部门的风险级别配置信息进行调整。  \n### 1.7.5. 轨迹分段查询  \n# 1） 车辆检索  \n可通过所属部门搜索车辆，在选择部门之后，页面左方会显示该部门所有车辆列表，而页面右方会默认显示第一辆车的行车记录信息，若在已选择所属部门的情况下输入车牌号检索，会检索该部门下的车辆；在没有选所属部门的情况下进行车牌号检索，则检索所有车辆。再选定查询的初始、截止日期，则检索该时段内车辆所有行驶的轨迹情况，并在右侧展现该时段内所有轨迹段。轨迹段列表中，每个轨迹段的信息包含起点时间、起点位置、终点时间、终点位置、行驶里程、分段油耗、停留时间、操作。如果终端类型不支持的数据类型则以“--”显示（如后视镜设备无油耗数据显示）。  \n![](http://10.15.1.75:18099/files/images/Userguide/cfb7acb15af20ba8ce726cec6287cca6641f3e0a695763903e5693cb852095f6.jpg)  \n# 2） 查看  \n点击展现的轨迹列表右侧查看按钮，可在下方地图上显示该轨迹段。  \n![](http://10.15.1.75:18099/files/images/Userguide/58503db710dadc2596b2c30781d47edddf54e2aff9058a9b020b379803829184.jpg)  \n可点击右上角，关闭地图页面，返回到轨迹列表页。  \n可点击播放，播放轨迹。  \n可对选中轨迹段进行播放，点击下方的播放按钮，则在地图上模拟车辆运行方向、位置、时间及速度信息。  \n数据筛选：勾选后可过滤速度为0 的轨迹点、勾选后可过滤经纬度为0 的轨迹点电子栅栏：勾选后将显示该车对应的电子栅栏（如果有设置）  \n![](http://10.15.1.75:18099/files/images/Userguide/120d84275ee230e1510131a6a2358b998b1fbb59c06a11651976f91fb66d743e.jpg)  \n### 1.7.6. 分段轨迹导出  \n主要进行部门车辆分段轨迹导出。根据时间范围和车辆，进行批量导出轨迹。  \n![](http://10.15.1.75:18099/files/images/Userguide/a324b6d06e61305743a96250985fc962d0899482ae27f96121d827bbc3847f55.jpg)  \n### 1.7.7. 电子围栏  \n# 电子栅栏分为新增电子栅栏和开关、详情、编辑删除电子栅栏。  \n![](http://10.15.1.75:18099/files/images/Userguide/ec83ec99af3dda5b93ee6a6cd307fd856f56c7eef160cd388c6ba13f56fcfe71.jpg)  \n# 1） 新增栅栏  \n基本设置：填写栅栏名称，选择监控车辆，绘制栅栏区域，支持绘制矩形、圆形、多边形、行政区域的电子栅栏，并采用透明色覆盖在地图上，如下图所示。  \n栅栏触发方式，包括驶入触发、驶出触发、驶入驶出触发；  \n栅栏触发时间，可配置每日栅栏触发的时间段，精确到分；  \n![](http://10.15.1.75:18099/files/images/Userguide/cdbf78435f7c88eaf5c8dddd2c4a81beadafc41dfc8cf28901922af43d7465a5.jpg)  \n# 2） 编辑栅栏  \n可以编辑栅栏名称，选择监控车辆，绘制栅栏区域，监控方式（默认为进入离开都报警），可以单个开启/关闭和删除电子栅栏。  \n### 1.7.8. 超速设置  \n首先以列表的形式显示已经设置好的超速提醒，并且可以新增区域、编辑和删除。  \n![](http://10.15.1.75:18099/files/images/Userguide/1ba1478db1b528acf505627bbd1bdeb416c25994091a4ac0585987e3b4834b31.jpg)  \n# 1) 新增超速提醒  \n首先在地图上选择区域，支持绘制多边形的电子栅栏，并采用透明色覆盖在地图上，  \n# 如下图所示，然后设置速度限制。  \n![](http://10.15.1.75:18099/files/images/Userguide/3de78c91113aeb304c83599608e4da419afac3d718e8c98c85b2b32207bb0c45.jpg)  \n设置成功后，当车辆在此区域行驶时，速度超过设置的速度值会有报警提醒消息。  \n### 1.7.9. 回场查询  \n可查询统计开启回场栅栏的车辆回场情况。  \n![](http://10.15.1.75:18099/files/images/Userguide/85cb647f47497544eb8ba9d478e7883d8167edc974796e6bbdff000575263a96.jpg)  \n### 1.7.10. 温湿度监控-  \n## 1.8. 费用管理  \n### 1.8.1. 记账本  \n对车辆使用过程中，产生的费用，可在此进行记录。  \n![](http://10.15.1.75:18099/files/images/Userguide/2e60cecb902049d1058849893dc5f14293a59d1398301575600156647e27a828.jpg)  \n# 1) 搜索查询  \n可通过车牌号码、车辆所属部门、责任人、费用类型、产生费用的时间等条件来搜索记账本。  \n# 2) 新增  \n可新增费用。责任人为产生此费用的人员，经办人为处理该次费用的人员。  \n![](http://10.15.1.75:18099/files/images/Userguide/b425260959ac23a494ff43fa6124a5e03ded645478213402591570b38c0c5451.jpg)  \n3) 编辑已产生的费用单，可再次编辑，以最终编辑的信息为准。  \n4) 删除对于误操作的记录，可删除。  \n### 1.8.2. 私车公用结算  \n企业对私车公用结算的记录，主要查询结算明细。  \n![](http://10.15.1.75:18099/files/images/Userguide/e8c99a1798d53a70aa50af1f6a33c106a98978871f672f01580e26b0a00f091a.jpg)  \n1) 搜索查询 可通过工单号、申请时间范围、车牌号、申请人和申请部门查询所有私车公用结算信息记录。  \n2）详情 点击后查看某一项私车公用结算详情。  \n3）打印 点击后对某一项私车公用结算信息进行打印。  \n4）批量打印 勾选多条记录，进行批量打印。  \n### 1.8.3. 计费规则管理  \n企业对计费规则进行管理，该规则主要影响派车流程中费用结算，按照里程、时长进行费用预估或结算。  \n![](http://10.15.1.75:18099/files/images/Userguide/bb46384f75e0903e8e457f93bf140e39922470352302a0f2233e5d997077c172.jpg)  \n# 新增：输入按照里程、时长计费相关参数，添加计费车型。  \n![](http://10.15.1.75:18099/files/images/Userguide/30a4987116916f0781e404dc5199317cd1ea31742d893edb366f2e4d68a77f14.jpg)  \n### 1.8.4. 用车预算管理  \n可对每个部门的用车预算进行查询和调整。  \n![](http://10.15.1.75:18099/files/images/Userguide/bcd8307eafa6bddcb48523c24e855adec043bfec523d0fa9695e6cd99bc8eba1.jpg)  \n新增：可对每个部门的预算进行初始化，后续可通过编辑按钮对预算进行调整。每个部门的预算每年只能初始化一次。  \n![](http://10.15.1.75:18099/files/images/Userguide/141e065a6e0b3f4564bc57bcacb2eb5ec1c37d996c4e2137eb764b644222c9f5.jpg)  \n### 1.8.5. 公务用车结算  \n公务用车结算，用户专门管理已完成的派单；统计由当天12:00 刷新前一天数据，管理员在已完成派单统计中查看实际里程及相关信息，并可对司机或用车人发起的里程申诉进行确认。  \n![](http://10.15.1.75:18099/files/images/Userguide/0266f0eea546cba5018a102f395415829b8c58d2db7c1c6001117cc1d91a2d1d.jpg)  \n公务用车结算的字段包括：工单号、用车模式、用车人、部门、车牌号码 、司机姓名、用车事由、用车人数、出发地点、到达地点、里程(km) 、申诉里程(km)（仅管理员及部门管理员可操作，仅有申诉的行程可以进行申诉里程修改）、计划开始时间、计划结束时间、计划用车时长、实际开始时间、实际结束时间、申诉事由（手机 APP 发起，表示用车人对该里程存在争议，需要管理员进行核对）。  \n### 1.8.6. 网约车订单结算  \n企业对网约车订单结算明细的记录，主要查询结算明细。  \n![](http://10.15.1.75:18099/files/images/Userguide/85afbb682df2570f0802f1aa0eae4fb2971800293f406c98c254d94266dc59c1.jpg)  \n1) 搜索查询 可通过工单号、行程编号、申请时间范围、车牌号、申请人、网约车平台、网约车订单编号查询所有网约车订单结算信息记录。  \n2）批量打印 勾选多条记录，进行批量打印。  \n## 1.9. 车务管理  \n### 1.9.1. 车队管理  \n#### 1.9.1.1. 车辆信息  \n![](http://10.15.1.75:18099/files/images/Userguide/5e4a3b2c1d0e9f8a7b6c5d4e3f2a1b0c9d8e7f6a5b4c3d2e1f0a9b8c7d6e5f.jpg)\n# 1) 车辆搜索  \n关键字搜索和筛选栏，可通过所属部门、车牌号、车辆别名、设备编码、设备类型、状态进行筛选。其中，列表关键字：车牌号，车辆别名，所属部门，在线状态，司机姓名，设备编码、设备类型。  \n# 2) 新增  \n点击新增按钮，弹出新增车辆弹窗，对新增车辆的信息进行记录填写（其中“\\*”为必填项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/8d80b28898babf82e5d832c172196f79e94e4d14c2e8ad18e60ac3fa3b336181.jpg)  \n3) 导出点击导出按钮，对显示列表里的车辆信息进行导出操作。  \n4) 批量新增  \n批量新增页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/1e1f8cdbd615b4f524d6dfc61633ad84c1dd22e3d6e8cc15a72e251d8542e424.jpg)  \n根据该弹窗内提供的下载模板，按文件字段要求填写多条车辆信息，进行车辆信息批量新增。  \n# 5) 批量编辑  \n批量编辑页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/0e672d44352930ba8fef860424e9a99c57d1f0e778ae31f937cb396caf9c1ad6.jpg)  \n根据该弹窗内提供的下载模板，按文件字段要求填写多条车辆信息，进行车辆信息批量编辑。  \n6) 批量绑定  \n批量绑定页面如下图所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/d027b176bb2690ef390448f387948436ba0b382878604fbb0d6d0e5cb8b7cc75.jpg)  \n根据该弹窗内提供的下载模板，按文件字段要求填写多条车辆信息，进行车辆信息批量绑定。  \n# 7) 批量提醒  \n先选择车牌号前复选框，点击批量提醒，出现弹窗，弹窗里有两个标签页，即 PUSH提醒设置和短信提醒设置。  \n![](http://10.15.1.75:18099/files/images/Userguide/70f56e24c9419fb81b7b4cf89352f41f4357ef7b90dcd81560db4d87dbb4e268.jpg)  \nPUSH 提醒设置里有状态开关，短信提醒设置里需要输入手机号码和状态开关。  \n8) 操作  \n对于每条车辆信息的记录里有六个功能，即当前位置、绑定、解绑、套餐、编辑、提醒设置  \na) 当前位置：是对车辆当前位置的显示。  \nb) 绑定：输入需要绑定的设备编码和IMEI 号进行绑定。  \nc) 解绑：将已绑定设备进行解绑。  \nd) 套餐：显示物联卡号、状态、套餐总流量、本月使用流量和本月剩余流量。  \ne) 编辑：对车辆现有信息进行编辑。  \nf) 提醒：进行PUSH 提醒和短信提醒。  \n#### 1.9.1.2. 司机管理  \n对司机的信息进行查询和管理，可在此页面看到司机的工作状态和绑定车辆。  \n![](http://10.15.1.75:18099/files/images/Userguide/bc3e334da28a9b6f67cbf297256d2ca69a520ce1a56b7b31d13d4cb1b6eaa846.jpg)  \n1）搜索查询可按照姓名、部门、手机号、工作状态、指派状态进行司机信息的匹配查询。  \n2）新增  \n新增按钮，弹出新增司机弹窗，对新增司机的信息进行记录填写（其中“\\*”为必填  \n项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/a0b58b66216ada031cfe27d4b991f7035a21d653b6c554d9f3e0cf3b76bcbe23.jpg)  \n3）导出点击导出按钮，对显示列表里的司机信息进行导出操作。  \n4）操作a) 编辑：对已存在司机的信息进行修改。b) 删除：对指定的司机信息进行删除。c) 详情：点击按钮查看司机更详细的信息。  \n#### 1.9.1.3. 人车匹配  \n车辆所属部门的管理员，对车辆和司机进行匹配或解绑，匹配后的车辆，才能在派单管理中分配给用车人使用。  \n![](http://10.15.1.75:18099/files/images/Userguide/98b4cf621243aa22b954ffbc7e01df602149531892f15fdca684993b1616f23f.jpg)  \n# 1) 搜索查询  \n可按照车辆所属部门、车牌号码、车辆别名、司机姓名、匹配起止时间进行查询，匹配状态。  \n2) 人车匹配可选择本部门所属车辆和司机，进行车辆匹配。  \n![](http://10.15.1.75:18099/files/images/Userguide/dcb5d672358f8bb150c1167daf771a29f474c98b209f558ef3485178bea0bb05.jpg)  \n3) 导出可将信息列表中的人车匹配信息进行导出操作。  \n4) 操作可对人车匹配进行解除绑定操作。  \n### 1.9.2. 日常车务申请  \n#### 1.9.2.1. 车务申请管理  \n针对公车管理的车务环节如维保、加油/充电、车险、年检等事务专门进行管理；1.企业管理员或部门管理员，可对申请单进行查看和处理。展示状态包括：待审批、已审批（审批人同意并选择到底结束）、已完成（执行实际金额确认操作）、已拒绝和已取消。  \n2.逐级审批级数最大 10 级。  \n3.管理员可对已审批的申请工单进行实际金额确认，并可上传附件。  \n![](http://10.15.1.75:18099/files/images/Userguide/4749c9e215ae44fb917d1c66d604e029d1d0bebc6ce04b087665679cd32de888.jpg)  \n1、同意：审批人通过此次申请，点击进行审批同意操作流程，同意时存在2 种操作（默认继续审批）；  \n![](http://10.15.1.75:18099/files/images/Userguide/8a563452a82fceecc79634cd034aebbd74e1ef0690a894a1da2a043c71a53af1.jpg)  \n继续审批：执行操作人审批通过并选择下一级审批人，可选择本企业的所有管理员；执行操作必须选择下一级审批人。  \n![](http://10.15.1.75:18099/files/images/Userguide/2517b43b241a5939bdb20d78352ee19884abcf146b2f6eacc640348cbe9e23b3.jpg)  \n到此结束：执行操作人审批通过并结束此流程。  \n2、拒绝：审批人拒绝此次申请，点击进行审批拒绝操作流程，拒绝必须输入拒绝理由。  \n![](http://10.15.1.75:18099/files/images/Userguide/d6462f28c1a89cd6e6805b2e987b1f555b0e199b2f9cfb0b9a53ee74fb73b795.jpg)  \n拒绝说明（200 字以内），必填。  \n3、实际金额：管理员和司机可进行实际金额确认，操作实际金额确认后，申请单状态变为已完成；实际金额必填，数值必须为整数或2 位小数，数值不能超过999999.99。  \n![](http://10.15.1.75:18099/files/images/Userguide/a8ee754f0cbd1e615024a5e076ac38f82f9c8eb4f4c88f2fac4eea7174ab6c46.jpg)  \n4 打印：预览打印内容，包括申请人、所属部门、申请日期、状态、车务类型、车牌号、司机、开始时间、结束时间、地点、预计金额、上次公里数、目前公里数、实际金额、申请说明、实际确认说明。权限：已审批、已完成、已取消、已拒绝状态的工单均可打印。  \n#### 1.9.2.2. 我的车务申请  \n对当前用户提交的维保、车险、年检等车务申请单进行查询和信息管理。  \n![](http://10.15.1.75:18099/files/images/Userguide/29b2faf3eff329eaff3213d9c5917eaeb8be2aa9a6f42e9b1526c58ad1e4819c.jpg)  \n1）搜索查询可按照工单号、工单状态信息的匹配查询。  \n2）新增  \n新增按钮，弹出新增申请弹窗，对新增车务的信息进行记录填写（其中“\\*”为必填  \n项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/4e8689e212df735d571aefd3c01aae94d86aa1916bcdec7f6a5371f98ce9b329.jpg)  \n# 3）导出  \n点击导出按钮，对显示列表里的申请单信息进行导出操作。  \n4）操作a) 编辑：可对待审批状态的车务申请单进行再编辑操作。b) 实际金额：对已审批状态的车务申请单进行金额确认。  \n) 详情：点击按钮查看申请单更详细的信息。  \n### 1.9.3. 日常车务管理  \n#### 1.9.3.1. 维保管理  \n对车辆的维保记录进行查询和手动录入，维保记录里的信息可同步到保养计划里面。  \n1）维保记录搜索  \n按照车牌号、部门、费用产生时间、登记号、来源、实际总金额进行对维保记录信息的匹配查询。  \n![](http://10.15.1.75:18099/files/images/Userguide/562c5db1a86dc7bd3c21621c702e2c9ca785fc1fc465a67a21f5e5804599065b.jpg)  \n# 2）新增  \n新增按钮，弹出新增维保记录弹窗，对新增维保的信息进行记录填写（其中“\\*”为必填项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/f6f19a452c9ff4a202d5ed45d12bbb46801a1326536b78860272cfe4696a605e.jpg)  \n# ■材料费用  \n<html><body><table><tr><td>序号</td><td>计量单位</td><td>材料名称</td><td>材料型号</td><td>供应商</td><td>电话</td><td>数量</td><td>单价(元)</td><td>总价(元)</td><td>操作</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td>暂无数据</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>新增</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>  \n# 1工时费用  \n# 3）保养计划  \n保养计划页面的记录跟随车辆的新增或删除动态增减，若在维保管理页面新增了一条保养记录，且记录里面填写了下次保养公里数和下次保养时间，则会更新对应车辆的保养计划，具体页面如下图所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/be2ee37bce6fadfa985dbfdcc9b17c60d75594cdc28e2bd0d5f7fbe14ee4ec23.jpg)  \n#### 1.9.3.2. 车险管理  \n对车辆的车险购买记录进行查询和手动录入，车险购买记录里的信息可同步到车辆保险计划里面。  \n1）车险记录搜索  \n按照车牌号、部门、费用产生时间、登记号、来源、实际总金额进行对维保记录信息的匹配查询。  \n![](http://10.15.1.75:18099/files/images/Userguide/c9485488a1621ca40db2689389d8ed3ba073480bf9006e6291d4a093b885866b.jpg)  \n# 2）新增  \n新增按钮，弹出新增车险记录弹窗，对新增维保的信息进行记录填写（其中“\\*”为必填项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/2418b44126f75575eac73a7bfde3efe865b2f781695c2964e4ce224689e50efc.jpg)  \n# 3）车辆保险计划  \n保险计划页面的记录跟随车辆的新增或删除动态增减，若在车险管理页面新增了一条保险记录，且记录里面填写了保险的到期时间，则会更新对应车辆的保险计划，具体页面如下图所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/d317c011fe2cac9f726b918f510f6c4b921c6675a6c999a15cee570e0adc97b3.jpg)  \n#### 1.9.3.3. 年检管理  \n对车辆的年检记录进行查询和手动录入，年检记录里的信息可同步到年检计划里面。  \n# 1）年检记录搜索  \n按照车牌号、部门、费用产生时间、登记号、来源、实际总金额进行对年检记录信息的匹配查询。  \n![](http://10.15.1.75:18099/files/images/Userguide/454d3b2ddb1667de06cee943cf3ec21a48e99c559c38366e2f17260d71d99fc0.jpg)  \n# 2）新增  \n新增按钮，弹出新增年检记录弹窗，对新增年检的信息进行记录填写（其中“\\*”为必填项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/fbbeb782373961b6fdaa86fe534785152a3b820b44a94b59f839abd3a0984e02.jpg)  \n# 3）年检计划  \n年检计划页面的记录跟随车辆的新增或删除动态增减，若在车险管理页面新增了一条保险记录，且记录里面填写了保险的到期时间，则会更新对应车辆的保险计划，具体页面如下图所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/3c068c43c1098efcaa3f07b9ae2767c95dec5725fc56571e37685045721398f0.jpg)  \n#### 1.9.3.4. 能耗管理  \n对用车过程中产生的能耗记录进行查询展示。  \n![](http://10.15.1.75:18099/files/images/Userguide/63634d672d47e5de0a00201700fa0d4830ffc7352e195b3659d1eac6c2f5d381.jpg)  \n1) 搜索查询  \n可按照车牌号、部门、费用产生时间、登记号、来源、实际总金额进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增能耗记录弹窗，对新增能耗记录的信息进行记录填写（其中“\\*”为必填项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/986f0ff05e26bbb53b1cd01f07a362396f904c48afdca7ec84ac6e710aff7b1e.jpg)  \n# 3) 导入  \n通过指定的模板可对能耗记录批量进行导入。  \n![](http://10.15.1.75:18099/files/images/Userguide/a7a9816c4441421b972fafd4817965b5732696cc3b25a8311bcb5a5fe4560681.jpg)  \n4) 导出  \n点击导出按钮，对显示列表里的车辆信息进行导出操作。  \n### 1.9.4. 其他车务管理  \n对用车过程中洗车、违章等车务所产生的费用记录进行查询展示。  \n![](http://10.15.1.75:18099/files/images/Userguide/9b23dba7a858327a5daa83fc338ef3f8ed037309bdb75ed992480c04cfa1a0f6.jpg)  \n1) 搜索查询可按照车牌号、部门、费用产生时间、登记号、来源、实际总金额进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增其他车务弹窗，对新增其他车务的信息进行记录填写（其中“\\*”  \n为必填项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/a2e4c71e776ef19379b53e0f4c9873a8e840ded1f196d33d9a600b8835c7815c.jpg)  \n# 3) 导入  \n通过指定的模板可对其他车务记录批量进行导入。  \n![](http://10.15.1.75:18099/files/images/Userguide/a8efde9e22de5bc16025727679e6674402f806cd6e440b7ea0fc11a149994858.jpg)  \n4) 导出点击导出按钮，对显示列表里的车辆信息进行导出操作。  \n### 1.9.5. 临时用车管理  \n对临时用车的记录进行管理和查询，不涉及审批流程。  \n![](http://10.15.1.75:18099/files/images/Userguide/889a8bcdbe87fd0e9942ed708c2b99aea49fbb447d2d36f55adbcc4429a67a34.jpg)  \n1) 搜索查询可按照车牌号、所属部门进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增临时用车弹窗，对临时用车的信息进行记录填写（其中“\\*”为  \n必填项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/96d729dfff0344aa9532022ff5a9adc89bde82b23e0b806d14345721ae0d762d.jpg)  \n### 1.9.6. 服务商管理  \n对服务商的信息进行管理和查询。  \n![](http://10.15.1.75:18099/files/images/Userguide/6109caf610bedff9cb2432e54bd4d94871e2df1951b3986bce9843ef5595eeea.jpg)  \n1) 搜索查询可按照所属部门、服务商名称、服务商类型、联系人、联系电话进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增服务商弹窗，对服务商的信息进行记录填写（其中“\\*”为必填  \n项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/58fb4a6322a7957d9ecc33bebe01676527427dd14cec04ad2c98625eff706fac.jpg)  \n### 1.9.7. 到期提醒查询  \n对年检、保养、保险相关的提醒事件进行查询，到期信息来自维保管理、保险管理、年检管理。  \n![](http://10.15.1.75:18099/files/images/Userguide/cee92f007af117991735301ec19d9c71db47bb513f2b7db1ad55e336ee29bff9.jpg)  \n# 1) 搜索查询  \n按照车牌号、所属部门、提醒类型（保险|年检|保养）、处理状态、发生时间进行匹  \n配查询。  \n2) 处理  \n处理按钮，弹出处理弹窗，对需要完善的信息进行填写（其中“\\*”为必填 项），  \n处理完成后对应的记录处理状态变为已处理，页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/7490bfb65da2a0a2953e6b4c9f024531511daf889b18ae6af7cc1bfa1111c7cb.jpg)  \n### 1.9.8. 车辆配备申请  \n#### 1.9.8.1. 编制额度申请  \n对编制额度进行申请和记录。  \n![](http://10.15.1.75:18099/files/images/Userguide/e8ac1f18159ce4a0f4847ec4fa14cbd6d60209b0348d082fd838772516b15a82.jpg)  \n1) 搜索查询可按照工单号、工单状态、申请时间开始和结束和部门进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增申请弹窗，对编制额度的信息进行记录填写（其中“\\*”为必填  \n项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/2d4eff5944f6fbda4b56fe4abdeea61b83a5ee6de41dd3591343ee3234173b4f.jpg)  \n3) 撤回待审批的申请，可以点击撤回，进行撤回申请。  \n4) 详情点击详情，查看申请详情信息。  \n#### 1.9.8.2. 车辆编制申请  \n# 对车辆编制进行申请和记录。  \n![](http://10.15.1.75:18099/files/images/Userguide/94cdb0e5a41bd14ca2e70a1b5d26b5e49308ebaa0211fce58f7310ca43cd9286.jpg)  \n1) 搜索查询可按照工单号、工单状态、申请时间开始和结束、部门、公车类型进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增申请弹窗，对车辆编制的信息进行记录填写（其中“\\*”为必填  \n项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/9bb00401c666b2784a0143c01524722c6f8f498448b6d25ea42ffec923b54edf.jpg)  \n3) 撤回待审批的申请，可以点击撤回，进行撤回申请。  \n4) 详情点击详情，查看申请详情信息。  \n#### 1.9.8.3. 车辆购置申请  \n对车辆购置进行申请和记录。  \n![](http://10.15.1.75:18099/files/images/Userguide/d4fcd029cb72e48c9bcac9850b6b959911c3842274a60a33451ad4c57cddc110.jpg)  \n1) 搜索查询可按照工单号、工单状态、申请时间开始和结束、部门进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增申请弹窗，对车辆购置的信息进行记录填写（其中“\\*”为必填  \n项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/26bf2ca956853a41cdba008de0bbff2a642e48fe63fb4e21aaa3f4ba2c57c054.jpg)  \n# 3) 撤回  \n待审批的申请，可以点击撤回，进行撤回申请。4) 详情  \n点击详情，查看申请详情信息。  \n#### 1.9.8.4. 车牌号申请  \n对车牌号进行申请和记录。  \n![](http://10.15.1.75:18099/files/images/Userguide/3134814ca29a053e8c5961e6697d35f0012cb9b3176d09bb846da31524b0fea1.jpg)  \n# 1) 搜索查询  \n可按照工单号、申请部门、工单状态、申请时间开始和结束、车牌号进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增申请弹窗，对车牌号的信息进行记录填写（其中“\\*”为必填项），  \n页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/dd8171da6e8b80f92608f114a558700333d24fa3d9142277f45ada22789774a5.jpg)  \n3) 撤回待审批的申请，可以点击撤回，进行撤回申请。  \n4) 详情点击详情，查看申请详情信息。  \n#### 1.9.8.5. 车辆处置申请  \n对车辆处置进行申请和记录。 \n![](http://10.15.1.75:18099/files/images/Userguide/98ab76cd34ef1290a5b6c7d83e4f5a69b0c1d2e37f4a5968b7c0d9e2f3a1b4c.jpg)\n1) 搜索查询  \n可按照工单号、工单状态、申请时间开始和结束、部门、车牌号和处置类型进行匹配查询。  \n2) 新增  \n新增按钮，弹出新增申请弹窗，对车辆处置的信息进行记录填写（其中“\\*”为必填  \n项），页面如下所示：  \n![](http://10.15.1.75:18099/files/images/Userguide/e02cc0c648a01b5e1abde8e485bb8c59a97cbad787db57e71b133f4a70aec32e.jpg)  \n3) 撤回待审批的申请，可以点击撤回，进行撤回申请。  \n4) 详情  \n点击详情，查看申请详情信息。  \n### 1.9.9. 车辆配备管理  \n#### 1.9.9.1. 编制额度管理  \n# 对编制额度进行管理。包括审批、调整，查看详情。  \n![](http://10.15.1.75:18099/files/images/Userguide/c0c700011ad54fbe61450385d468f2ac18aa26a34a695f666bb8cf34e16c5095.jpg)  \n1) 搜索查询可按照工单号、工单状态、申请时间开始和结束、部门进行匹配查询。  \n2) 导出导出按钮，将查询的信息导出成Excel 文档保存。  \n3) 详情查看编制额度详情。  \n4）待我审批|我已审批页面展示需要我审批的申请信息和我已经审批过的申请信息。  \n#### 1.9.9.2. 编制查询  \n![](http://10.15.1.75:18099/files/images/Userguide/03e7801d0ab74607d474bbe5505dd521c0a1cc56cffcc6d6f7284b332b06f833.jpg)  \n#### 1.9.9.3. 车辆编制管理  \n对车辆编制进行管理。包括审批、查看详情。  \n![](http://10.15.1.75:18099/files/images/Userguide/465be55a11af33eedcf839e263798ca83f7dd402f98f48553fc76c7068ef5fcd.jpg)  \n1) 搜索查询可按照工单号、工单状态、申请时间开始和结束、部门和公车类型进行匹配查询。  \n2) 导出导出按钮，将查询的信息导出成Excel 文档保存。  \n3) 详情  \n查看车辆编制详情。  \n4）待我审批|我已审批  \n页面展示需要我审批的申请信息和我已经审批过的申请信息。  \n#### 1.9.9.4. 车辆购置管理  \n# 对车辆购置进行管理。包括审批、查看详情。  \n![](http://10.15.1.75:18099/files/images/Userguide/8197852f22d14b724f74129f7678cf5ff0f20d7392b0952b50b53d7979fdfbe7.jpg)  \n1) 搜索查询可按照工单号、工单状态、申请时间开始和结束、部门进行匹配查询。  \n2) 导出导出按钮，将查询的信息导出成Excel 文档保存。  \n3) 详情查看车辆购置详情。  \n4）待我审批|我已审批页面展示需要我审批的申请信息和我已经审批过的申请信息。  \n#### 1.9.9.5. 车牌号管理  \n# 对车牌号进行管理。包括审批、查看详情。 \n![](http://10.15.1.75:18099/files/images/Userguide/f0e1d2c3b4a59687f6e5d4c3b2a1908f7e6d5c4b3a2918f0e1d2c3b4a596877.jpg)\n1) 搜索查询可按照工单号、工单状态、申请时间开始和结束、部门和车牌号进行匹配查询。  \n2) 导出导出按钮，将查询的信息导出成Excel 文档保存。  \n3) 详情查看车牌号详情。  \n4）待我审批|我已审批页面展示需要我审批的申请信息和我已经审批过的申请信息。  \n#### 1.9.9.6. 车辆处置管理  \n# 对车辆处置进行管理。包括审批、查看详情。  \n![](http://10.15.1.75:18099/files/images/Userguide/1a7f3e9d0c8b2a596f4d7c1b3e5f8a9d2c4b6a0d9f8e7c1b3a596f4d2c1b0e9f.jpg)\n1) 搜索查询  \n可按照工单号、工单状态、申请时间开始和结束、部门、车牌号和处置类型进行匹配查询。  \n2) 导出导出按钮，将查询的信息导出成Excel 文档保存。  \n3) 详情  \n查看车牌号详情。  \n4）待我审批|我已审批  \n页面展示需要我审批的申请信息和我已经审批过的申请信息。  \n## 1.10. 统计报表  \n#### 1.10.1. 车辆监控统计  \n#### 1.10.1.1. 里程油耗统计  \n可对企业或部门在一段时间内的里程、油耗、用车时间进行查看。  \n可选择按时间、按部门或按车辆进行查看。并可选择统计时间范围，以及统计的部门和具体车辆。  \n![](http://10.15.1.75:18099/files/images/Userguide/f520b22adc8b77c99f7312df148a93ba48f6cb344e2a7c6914b930d168a860c6.jpg)  \n#### 1.10.1.2. 电子围栏里程统计  \n可查看车辆在电子栅栏范围内的里程统计。  \n![](http://10.15.1.75:18099/files/images/Userguide/834da425142dad82ea446184b030a57619b6a7ed50270de7c08a21494e4f3d06.jpg)  \n#### 1.10.1.3. 区间停留统计  \n查询车辆在指定地点停留的时长统计记录。  \n![](http://10.15.1.75:18099/files/images/Userguide/d91600e9560350b407fa9319a79a21bb7af6427f54e36f8b7dd1ab9b6945adc1.jpg)  \n### 1.10.2. 车辆运营统计  \n#### 1.10.2.1. 加班统计  \n可对部门和车辆的加班情况进行统计。  \n可选择部门、车辆，以及加班的起止时间。并可自定义加班的起止时间段（半小时的粒度）。  \n![](http://10.15.1.75:18099/files/images/Userguide/99b84f798e0c64644b74447b079828aab2b74c1a1a6af0816f32c50927e338c4.jpg)  \n#### 1.10.2.2. 车辆运营统计  \n# 按照车辆统计派单情况，查看每辆车的里程、用车次数等数据统计。  \n![](http://10.15.1.75:18099/files/images/Userguide/d7cb62e68fc1b5ad566f22c5d249782a7a0af2350d9288a152374979f5d1b12f.jpg)  \n#### 1.10.2.3. 车队运营报告  \n该服务为增值服务，默认全部不开通；  \n【设置】-【服务管理】- 找到已开通增值服务 -【编辑】- 找到运营报告场景，点  \n![](http://10.15.1.75:18099/files/images/Userguide/d216d02fd1dcc869499aff8b1dda40beacb2669ffa3ab9b0c4b27ece188ef448.jpg)  \n点击筛选报考类型，可查看月报、季报、年报；时间可分别选择月份、季度、年份：  \n![](http://10.15.1.75:18099/files/images/Userguide/50efe7b463fce1ec7ce812c736d21009deb7f018bc8493a537efa0592799ee65.jpg)  \n（1）导出数据看板  \n选择报告类型和时间后，点击【导出数据看板】，支持在本地下载数据看板，文件格式为 pdf；  \n若筛选条件有一个为空，点击【导出数据看板】，页面上方提示”请选择报告类型和对应的统计时间”。  \n（2）导出运营报告  \n选择报告类型和时间后，点击【导出运营报告】，支持在数据中心查看下载的运营报告；  \n若筛选条件有一个为空，点击【导出运营报告】，页面上方提示”请选择报告类型和对应的统计时间”。  \n#### 1.10.2.4. 燃油数据统计  \n可导入外部报表并组成新的燃油数据进行分析导出。  \n![](http://10.15.1.75:18099/files/images/Userguide/704010f08cb709dfb9a462438fb6b8f39066c632f96f5e951c99c11033a7701e.jpg)  \n### 1.10.3. 异常用车统计  \n可对车辆的异常使用情况，如车机拆除（OBD）、越界、超速的次数，进行统计。  \n可分别按照时间、部门和车辆来进行查看，并可选择统计的起止时间，以及测试的部门。  \n![](http://10.15.1.75:18099/files/images/Userguide/4e895d5db69ddc1f383c5ea08ac2beee3e24af6b0ab85f55c8440bebb5b9df8a.jpg)  \n### 1.10.4. 费用统计  \n统计选择的部门，在所选时间段内，产生的各类费用分别是多少。  \n![](http://10.15.1.75:18099/files/images/Userguide/8a989923a3917538dad2a1e858aa55796a8e11b9bd618e5f2a157512b529252a.jpg)  \n### 1.10.5. 司机用车统计  \n可对本部门司机的用车情况进行统计。  \n# 可选择司机的部门、司机姓名、统计起止日期。  \n![](http://10.15.1.75:18099/files/images/Userguide/30777024253eebca2578f81ed7ff61e85633a0d91865442b202ced9bb68ce646.jpg)  \n## 1.11. 企业管理  \n### 1.11.1. 基础信息  \n#### 1.11.1.1. 用户账号管理  \n# 账号权限管理角色分为企业管理员、部门管理员、司机和用车人。  \n![](http://10.15.1.75:18099/files/images/Userguide/e898def4f765b5dc1caba35243d07504e912e0edec9164e63b0933f3e58d621f.jpg)  \n1. 进入该页面时，默认显示该账号所能管理的所有账号，并分页显示具体为：  \n1）企业管理员：可查看本企业，及该企业下级部门的部门管理员、司机和用车人的  \n账号。  \n2）部门管理员：可查看本部门，及该部门下级部门的部门管理员、司机和用车人的账号。  \n3）用车人：登录无此菜单。  \n注：司机无法登陆PC 端，仅可登陆APP。  \n2. 列表操作权限：  \n1）企业管理员：  \n（1）创建与本账号权限相同的企业管理员角色账号。  \n（2）可以对该企业所有企业管理员、部门管理员、用车人、司机的账号进行编辑、删除、密码重置、详情（查看）的操作；  \n（3）可对自身账号进行编辑、密码重置、详情的操作；  \n2）部门管理员：  \n（1）可创建与本账号权限相同的部门管理员角色账号。（2）可对该部门的部门管理员、用车人和司机进行编辑、删除、密码重置、详情（查看）的操作；（3）可对自身账号进行编辑、密码重置、详情的操作；（4）不可见其他部门的账号，并进行操作。  \n![](http://10.15.1.75:18099/files/images/Userguide/fd08ba8d80dcbbed7c10eab0b127dc630d15b19a87450345e5c6545931fd9c8b.jpg)  \n不同层级部门可创建不同角色。  \n#### 1.11.1.2. 组织部门  \n部门管理员仅可新增、修改和删除自己的下级部门。  \n![](http://10.15.1.75:18099/files/images/Userguide/a916257d71b9c0132df5c111c5be3e734669b7e4a9ab318c37d1dfe0b1282b56.jpg)  \n点击右上角“新增”可新增企业部门信息。其中标\\*号为必填项。企业可根据自身实  \n际 部门管理架构进行设置。  \n![](http://10.15.1.75:18099/files/images/Userguide/511e33dae8439dafce35b1009ba2874401ba782ccd178a9589947c708286780a.jpg)  \n#### 1.11.1.3. POI 管理  \n企业管理员可在地图上根据实际需要定义兴趣点，以便快速的在地图上找到自己所需要的位置。  \n![](http://10.15.1.75:18099/files/images/Userguide/e2799259eb2c948288e7bda8ce5afa2147c1996ac830745fd9c8376b563cd120.jpg)  \n#### 1.11.1.4. 电子签章管理-  \n### 1.11.2. 流程管理  \n管理员可根据企业需要，预先设置不同审批流程的用车模式；用车模式可自行命名，选择使用范围(部门)以及审批级数（审批级数最多可选择 5 级，每一级可最多同时选择3个审批人），且可以选择是否开放评价。  \n![](http://10.15.1.75:18099/files/images/Userguide/720b292c5ca448cf949149e023f8707c618391b836b66bc7cd270a20214ec3fb.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/727a697ab5dafa6fc900fa6d01c591c842f631d072ec1efcfa62afdf5ef6411c.jpg)  \n### 1.11.3. 公告管理  \n#### 1.11.3.1. 网页公告  \n管理员可自定义系统公告，公告内容可包含文字、图片、表格、超链接。公告发布期间，生效部门的用户登录平台即可第一时间看到公告。  \n![](http://10.15.1.75:18099/files/images/Userguide/b54abbc830f4b6aace80509fe3c608b70dc6d82566e6dd9045a7beb820a93057.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/46b290a9ea8d435b3812f16abdee428ce9a07b2d1578ef038a1ba07df5d962ad.jpg)  \n#### 1.11.3.2. 车队公告  \n管理员可在此对选定的部门和选定的角色（司机或用车人）发送新公告，或查看已发送的公告。  \n![](http://10.15.1.75:18099/files/images/Userguide/dd249a83116b687ceb9573fab84cf76d6cf62441794c66ca1b1c6a11787f2dd2.jpg)  \n# 1) 搜索查询  \n关键字搜索和筛选栏，可通过创建人、公告部门、创建日期进行筛选。未搜索时，则按照公告创建的时间，由最新开始展示。  \n# 2) 新增  \n可选择公告的部门及公告对象，则该部门及其下属部门对应对象可收到公告：  \n![](http://10.15.1.75:18099/files/images/Userguide/45dee324644de65eff02eff7d8d90c504788effe53ef1995c4312875d5092635.jpg)  \n#### 1.11.3.3. 安装评价  \n管理员可在此对服务商的安装数量进行记录，以及服务质量进行评分。  \n![](http://10.15.1.75:18099/files/images/Userguide/0462ea453b0f5700f25d0b75e8856c3c916cb252e1e35809330a3a07396c0a28.jpg)  \n# 2. 重点功能介绍  \n## 2.1. 派单流程  \n### 2.1.1. 使用端说明  \n（4）支持用车人、管理员在 APP 端或者web 端进行用车申请、行程确认、用车评价和查看历史申请记录操作；（5）支持司机在APP 端进行接单、执行用车任务、结束用车和里程申诉等操作；（6）支持管理员在APP 端或者 web 端进行用车审批、派车和查看部门历史用车工单。  \n### 2.1.2. 核心流程  \n（3）主要操作流程：用车人提交用车申请后，按照该订单指定的审批流程进行管理员审批和调度车辆司机（默认为审批模式的最后一个节点上的管理员进行调度司机和车辆），指派车辆后，司机可以在 APP 上查看并执行用车任务，司机用车结束后，工单完成。  \n（4）工单状态说明：  \n$\\textcircled{1}$ 待审批：用车人提交用车申请以后工单状态为“待审批”；  \n$\\textcircled{2}$ 已审批：审批流程上的管理员全部审批和指派车辆司机后，工单状态为“已审批”；  \n$\\textcircled{3}$ 执行中：司机点击【开始用车】按钮开始执行用车任务，单子状态变为“执行中”；  \n$\\textcircled{4}$ 已完成：司机点击【结束用车】按钮结束用车任务，单子状态变为：已完成“；  \n$\\textcircled{5}$ 已拒绝：管理员审批用车单时拒绝了该单子，工单状态变为”已拒绝“，”已拒绝“的单子用车人无法重新提交；  \n$\\textcircled{6}$ 已取消：用车人提交用车申请以后，在管理员审批之前（工单状态为”待审批“）可以撤回该申请，撤回后工单状态变为”已取消“，用车人可以重新编辑后再次提交；  \n$\\textcircled{7}$ 已中止：司机点击【开始用车】按钮开始执行用车任务之后，已审批该工单的管理员具备“中止”该单子的权限，中止后单子状态变为“已中止”。  \n![](http://10.15.1.75:18099/files/images/Userguide/483dd12312894cf95410943919fabf6a2c1055b46ae2a659838ef544265eebee.jpg)  \n用车主要操作流程  \n![](http://10.15.1.75:18099/files/images/Userguide/4cd23b313048f06d030a5e0c9ddc2fcea0d306ddfb5853e05d0784961dca171f.jpg)  \n工单状态流程图  \n（3）其他操作说明：  \n$\\textcircled{1}$ 中止：司机点击【开始用车】按钮开始执行用车任务之后（工单状态为“执行中”），已审批该工单的管理员具备“中止”该单子的权限，中止后单子状态变为“已中止”；$\\textcircled{2}$ 变更：当工单状态为“待审批”、“已审批”或者“执行中”时，用车单申请人的直属管理员及上级管理员具备【变更】权限，可以变更该工单的“计划结束时间”和“计划到达地点”，变更后用车详情中的计划结束时间和计划到达地点会自动更新；  \n![](http://10.15.1.75:18099/files/images/Userguide/8048a3d15ac71a820a3479d6cb0f67ebc1a36af74631831a4cf4b8442e6483b7.jpg)  \n$\\textcircled{3}$ 改派：最后一个审批节点的管理员审批单子后，工单状态变为“已审批”，此时审批节点上的管理员具备改派车辆和司机的权限，改派后原指派司机无法接单，被指派的新司机可以进行接单操作；  \n$\\textcircled{4}$ 完成：如果单子处于“执行中”状态，审批流程上的最后一个审批的管理员，具备完成该工单的权限，点击【完成】后工单状态变为“已完成”；  \n$\\textcircled{5}$ 评价：用车完成后，该工单的用车申请人可以通过APP 或者web 评价该工单，提交评价后也可以重新评价。  \n![](http://10.15.1.75:18099/files/images/Userguide/2d64bacbc583148d76c8c162f941174a6222788d0c987be8425c1cbf6b9e6ffe.jpg)  \nweb 端用车评价  \n![](http://10.15.1.75:18099/files/images/Userguide/81c959e5c8066d3cffc478d09abd661fd1394d9731c2265bd2eed82044bfea7e.jpg)  \n$\\textcircled{6}$ 行程确认：用车完成后，用车人可以在web 端或者APP 端确认行程，确认行程后APP端该工单详情页面，会展示“已确认行程”标识。  \n![](http://10.15.1.75:18099/files/images/Userguide/a525273633f9dd7bbcf0312b6eb68ad37bee01ac3ecc49818e9af561ae5a3439.jpg)  \nweb 端行程确认  \n![](http://10.15.1.75:18099/files/images/Userguide/eff39e4cc10f21cee794bb8690ae3c6d62fa266feee682ebfeb990cd15de2eb9.jpg)  \n# APP 端行程确认  \n$\\textcircled{7}$ 申诉：用车完成后，用车人和司机均可对该工单的里程进行申诉。  \n若为司机申诉，司机提交申诉后，该工单的申请人需要审批申诉内容，审批同意后，申请人的直属管理员可以在【公务用车结算】中查看并根据申诉内容修改里程数据。若用车人不认可，则申诉失败；  \n![](http://10.15.1.75:18099/files/images/Userguide/a2f37b1aacfbe5b26d959696a9aabcf764ce2df56726031cc2dd51661ea307af.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/e33fa9d3870c15b5dc1be69e7541b44280944fb012ffacf1890067c2af8356f6.jpg)  \n用车人审批申诉  \n![](http://10.15.1.75:18099/files/images/Userguide/5ffd89df715d345c735f93efb54283f7b89d7e64f3f4914e3145716d95ab3d4c.jpg)  \n若为用车人申诉，用车人提交申诉后，该工单申请人的直属管理员可以在【公务用车结算】中查看并根据申诉内容修改里程数据。  \n![](http://10.15.1.75:18099/files/images/Userguide/22dd707fa9d654b0b3362bf8361181e68da8d5f51cc8d8ac073e00410b9e5580.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/3851797bb58defb4dc8dfb9def85195c15cd9bedbe86f0e3192e7461a21aa4e6.jpg)  \n管理员查看申诉和修改里程  \n$\\textcircled{8}$ 费用补充：用车完成后，司机在可以 APP 端进行该工单用车费用的补充，包括停车费、过路费和其他费用等。xx 管理员可以在web 端-【公务用车结算】-用车费用下进行用车费用的调整和历史费用调整记录的查看。  \n![](http://10.15.1.75:18099/files/images/Userguide/a8525ebb15390bfcdbfc60c74aa144d8c0caed98f731b3dcaf5e294cc4f16cd1.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/0d8a983bff72022c5ccb3515a255c9ff6e7beca459bac3b9b6d688fbfc1c6d43.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/6f3d64e9388b77acf6e772e59064236c5f8eeddf1b40844f97462f4731c173da.jpg)  \n管理员调整用车费用  \n### 2.1.3. 主要功能操作  \n#### 2.1.3.1. 用车申请  \n（1）web 端  \n点击web【用车管理】-【公务用车申请】-【新增申请】按钮新增申请页面，填写审批方式、用车模式、用车人、用车事由、起止时间和起止地点等必填字段后，即可发起用车申请。  \n补单、批量申请填写内容和新增申请一致，其中批量申请可以提交多条申请，但需注意批量申请模板中，“用车人”字段每行仅能填写一位用车人名称。  \n![](http://10.15.1.75:18099/files/images/Userguide/fea3df8c152913100ea13047abe1d8c3eb51364a4094935f5e63623c238f7b54.jpg)  \n# 用车申请入口  \n![](http://10.15.1.75:18099/files/images/Userguide/ee6c2e15b48824a51f85aa60d4e27accd7e9574674583cf247cdb9d0be9e4bd5.jpg)  \nweb 端新增用车申请  \n![](http://10.15.1.75:18099/files/images/Userguide/bfad1f4a217e924092575f55e9f016786cac8e70b5c733c978c121bb3efa53e3.jpg)  \n批量申请模板  \n![](http://10.15.1.75:18099/files/images/Userguide/c0ffee1234567890deadbeefcafebabe0facade1234567890abcdef012345678.jpg)\n# （2）APP 端  \n用车申请人在APP 点击【用车申请】-右上角【新建申请】进入新增申请页面，填写用车模式、用车事由、用车人、计划起止地点和计划起止结束时间等信息。发起申请后，申请人可以进行工单撤回操作。  \n![](http://10.15.1.75:18099/files/images/Userguide/0cd68cd36ee9341b4b4f49cb8bd5fa51b4d6e49c0b0b15fda91b3e1cf53a94cc.jpg)  \n# 2.1.3.2. 用车审批和调度  \n（1）适用场景：用车人申请用车时，审批方式填写的是“已有用车模式”（以二级审批举例）。  \n$\\textcircled{1}$ web 端  \n一级审批：管理员应点击【用车管理】-【公务用车管理】-“我的待办”查看待审批处理的用车工单，点击【同意】或者【拒绝】按钮对该用车工单进行审批，同意或拒绝均可以填写相关备注说明。  \n点击【同意】工单将至下一个节点进行审批，点击【拒绝】，需填写拒绝原因，工单状态将变为“已拒绝”，用车人如希望继续申请用车需要重新新增申请。  \n![](http://10.15.1.75:18099/files/images/Userguide/bc5b7e35ee99705329d8cd9d8dc455407b8ca5db994e27e3fe6ff54d22ef294c.jpg)  \n二级审批调度：最后一级管理员需要对该工单的司机和车辆进行分配，点击【同意】，选择需要分配的司机和车辆。点击【拒绝】工单状态将变为“已拒绝”，用车人如希望继续申请用车需要重新新增申请。  \n![](http://10.15.1.75:18099/files/images/Userguide/7c476c30801fa01c7e86ad62f6fc5f9301ee56a1c398a2148a01e19d628a4b57.jpg)  \n# ②APP 端  \n一级审批：管理员应点击【用车管理】页面查看待审批处理的用车工单，点击【同意】或者【拒绝】按钮对该用车工单进行审批。点击【同意】工单将直接通过成功，然后至下一个节点进行审批，点击【拒绝】，需填写拒绝原因，工单状态将变为“已拒绝”，用车人如希望继续申请用车需要重新新增申请。  \n![](http://10.15.1.75:18099/files/images/Userguide/6f41ad416d81851129da7292ef4c56fd6f3fac9921fe07159ece1aa115e1a210.jpg)  \n二级审批调度：最后一级管理员需要对该工单的司机和车辆进行分配，点击【同意】，选择需要分配的司机和车辆。点击【拒绝】需填写拒绝原因，工单状态将变为“已拒绝”，用车人如希望继续申请用车需要重新新增申请。  \n![](http://10.15.1.75:18099/files/images/Userguide/8c59f1ff59594c969bf3e29dba1dbb2dcc1c714cf7a8165cebfdd2c56c8bb3c5.jpg)  \n# （2）适用场景：用车人申请用车时，审批方式填写的是“自选审批人”。  \n①web 端  \n一级审批：用车人直属的部门管理员应点击【用车管理】-【公务用车管理】-“我的待办”查看待审批处理的用车工单。  \n点击【同意】，在“同意用车”页面上，管理员需要决策该工单是否继续审批还是到此完结，若继续审批，需要指定下一级审批人，下一审批人也可以继续决定是否继续审批还是到此完结；若到此结束，管理员需要选择此次执行用车任务的车辆和司机。  \n![](http://10.15.1.75:18099/files/images/Userguide/d65dedeb1979ab5a6faed9735284ee9fc6c0858947e4c2e6cee713ca3aef3f51.jpg)  \n点击【拒绝】，需填写拒绝原因，工单状态将变为“已拒绝”，用车人如希望继续申请用车需要重新新增申请。  \n![](http://10.15.1.75:18099/files/images/Userguide/61da02b9bc8faddae064dfa007dc6d5b655f1dec10ff2140b6998057eb4f8938.jpg)  \n②APP 端  \n一级审批：用车人直属的部门管理员应点击【用车管理】列表查看待审批处理的用车工单。点击【同意】，在“同意申请”页面上，管理员需要决策该工单是否继续审批还是到此完结，若继续审批，需要指定下一级审批人，下一审批人也可以继续决定是否继续审批还是到此完结；若到此结束，管理员需要选择此次执行用车任务的车辆和司机。  \n![](http://10.15.1.75:18099/files/images/Userguide/462384c8bb2a457f249c26f96fe3eb67286663f61f49de10f498543c436be7bd.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/1d02a16e04288725ac4838117134c8b40ceb16f8d898dbb5b2003c3a6e07fbf8.jpg)  \n#### 2.1.3.3. 执行用车任务  \n司机在APP 端用车任务页面或者工单详情页面点击【开始用车】后，该工单可以确定执行开始用车，工单状态变为“执行中”。  \n![](http://10.15.1.75:18099/files/images/Userguide/ffd3ba093c71aa454562a5460d339fbd0a84744c5ec3d434d4c96b5edaf2e87b.jpg)  \n#### 2.1.3.4. 用车结束  \n工单状态处于执行中时，若用车任务结束，支持用车人在 APP 端或者web 端完成用车，或者司机在 APP 端结束用车，完成或者结束用车后，工单均变为”已完成“状态。  \n![](http://10.15.1.75:18099/files/images/Userguide/5b313d085969c2e16161d60fb123c906e768a616537152ab4ee5f48bbdfaf050.jpg)  \n司机APP 端结束用车  \n![](http://10.15.1.75:18099/files/images/Userguide/bcc011a6ce37b4368a0c6b5b16e13b6b4a2bf69503c9b4c90a699c5b26f4ee67.jpg)  \n![](http://10.15.1.75:18099/files/images/Userguide/97f1cd7f2eab164ee102347802c9d042fb37ccf5ab835adaf03f0e1d48fdf881.jpg)  \n用车人web 端结束用车  \n用车人APP 端结束用车  \n#### 2.1.3.5. 用车补单  \n“补单”审批流程与“新增申请”流程基本一致，唯一区别是“新增申请”的工单是先申请后用车，所以工单完成后有实际轨迹记录，而补单流程中没有实际的轨迹记录，只是单纯的事后信息记录，APP 端和web 端均有带“补”字标识区分。  \n![](http://10.15.1.75:18099/files/images/Userguide/153b690fc0a29a1879c03bf83e7ce1d1027580768218e770e7ff5c5b660d24c4.jpg)  \n## 2.2. 车务申请流程  \n#### 2.2.1. 使用端说明  \n支持管理员在 web 端或者司机从APP 端发起车务申请，管理员可以在web 端或者APP 端审批车务申请单，司机和已审批的部门管理员可以在车务任务完成后进行实际金额确认，并可上传附件。  \n### 2.2.2. 核心流程  \n# （1）主要操作流程：  \n管理员可以从 web 或者司机从APP 端发起车务申请，申请人可以指定审批流程，可以默认选择申请人所在部门的所有部门管理员进行审批，也可以选择本部门及下级部门的某个管理员进行审批，对应管理员审批后可以继续选择指定管理员审批，或者结束审批。结束审批的单子，已经审批的部门管理员或者发起申请的司机可以在申请页面填写该单子的实际维修金额，填写后工单完结。  \n![](http://10.15.1.75:18099/files/images/Userguide/481cb9b245efc40fff78cc925f4da1ba7c35e7dbe6e95fc382f21069d370fea4.jpg)  \n（2）工单状态说明：  \n$\\textcircled{1}$ 待审批：司机或者管理员角色发起“我的车务申请”后，工单状态变为“待审批”；  \n$\\textcircled{2}$ 已审批：管理员审批通过后，工单状态变为“已审批”；  \n$\\textcircled{3}$ 已完成：申请者或者管理员填写车务申请单的实际金额后，工单状态变为“已完成”；  \n④已取消：发起“我的车务申请”后，申请人撤回申请，撤回后工单状态变为“已取消”；  \n⑤已拒绝：管理员审批拒绝后，工单状态变为“已拒绝”。  \n![](http://10.15.1.75:18099/files/images/Userguide/315bc8a60354ef28b1c1a4791481c5c4fc0e7e145e221f40b33e762c70ceae9d.jpg)  \n### 2.2.3. 主要功能操作  \n#### 2.2.3.1. 车务申请  \n（1）web 端  \n管理员可以从web 端-【用车管理】-【日常车务申请】-【我的车务申请】-【新增】发起“我的车务申请”，申请时需要填写车务类型、是否涉及用车、车牌号、司机姓名、司机电话、起止时间、预计金额、地点、申请事由和审批流程等必填信息，其中审批流程可以选择申请人所在部门的所有部门管理员或者指定某个管理员，点击【确定】提交申请。  \n![](http://10.15.1.75:18099/files/images/Userguide/6d3f628b2b3a3d6b2d78f948b97022f45f2e50103fbbe4a980f0d169671c2e32.jpg)  \n# （2）APP 端  \n管理员或者司机可以在APP 端发起车务申请（原维修申请）-右上角加号，申请时需要填写车务类型、是否涉及用车、车牌号、司机姓名、司机电话、起止时间、预计金额、地点、申请事由和审批流程等必填信息，其中审批流程可以选择申请人所在部门的所有部门管理员或者指定某个管理员，点击【确定】提交申请。  \n![](http://10.15.1.75:18099/files/images/Userguide/7e2f9a1b3c8d0e5f6a7b8c9d0e1f2a3b4c5d6e7f8091a2b3c4d5e6f708192a3b.jpg)\n#### 2.2.3.2. 车务审批管理  \n（1）web 端  \n管理员可以从web 端-【用车管理】-【日常车务申请】-【我的车务申请】-【车务申请管理】页面查看待审批的车务申请单信息。点击列表上【详情】按钮，可以查看该车务申请单的详情信息。  \n如果审批通过，则点击【同意】，可以在“同意申请”页面确定是否继续审批，若继续审批则需要指定下一级审批人，若到此结束可以直接点击【确定】按钮表明审批通过。  \n如果拒绝车务申请，则点击【拒绝】，可以在“拒绝申请”页面备注拒绝说明，点击【确定】则表明拒绝审批。  \n![](http://10.15.1.75:18099/files/images/Userguide/c4f06ce56c1b201eb6f9154b58523c49705014b81cc25d22216ac0a640464918.jpg)  \n车务申请管理页面  \n■申请信息  \n<html><body><table><tr><td>申请事由</td><td colspan=\"3\">修车</td></tr><tr><td>申请人</td><td>刘巧测试</td><td>所属部门</td><td>中移物联网演示账号</td></tr><tr><td>车务类型</td><td>维保</td><td>是否涉及用车</td><td>是</td></tr><tr><td>申请时间</td><td>2024-03-04 10:41</td><td>工单状态</td><td>待审批</td></tr><tr><td>司机</td><td>小王</td><td>司机电话</td><td>15736229080</td></tr><tr><td>开始时间</td><td>2024-03-04 12:40</td><td>结束时间</td><td>2024-03-04 14:40</td></tr><tr><td>地点</td><td>重庆市渝北区大竹林街道 中移物联网有限公司中移 物联网大楼</td><td>车牌号</td><td>切换25012100013741</td></tr><tr><td>预计金额</td><td>0.00</td><td>实际金额</td><td>0.00</td></tr><tr><td>上次公里数</td><td></td><td>目前公里数</td><td></td></tr><tr><td>服务商</td><td colspan=\"3\"></td></tr><tr><td>申请说明</td><td colspan=\"3\"></td></tr><tr><td>实际确认说明</td><td colspan=\"3\"></td></tr><tr><td>申请附件</td><td colspan=\"3\"></td></tr><tr><td>实际确认附件</td><td colspan=\"3\"></td></tr></table></body></html>  \n■流程信息  \n<html><body><table><tr><td>当前流程 员-待审核</td><td colspan=\"5\">中移物联网演示账号管理</td></tr><tr><td>号管理员 审批结果</td><td colspan=\"5\">中移物联网演示账</td></tr><tr><td>NO.</td><td colspan=\"5\">审批人 审批时间</td></tr><tr><td></td><td colspan=\"2\">暂无数据</td><td></td><td></td><td></td></tr><tr><td colspan=\"5\">审批附件</td></tr></table></body></html>  \n![](http://10.15.1.75:18099/files/images/Userguide/db71270f45a5797c2f565a21a3a60b4fa4ebe4760e3b4d6691ae28735360d908.jpg)  \n拒绝车务申请  \n# （2）APP 端  \n管理员可以从APP 端-车务管理（原维修管理）页面审批车务申请单，如果审批通过，则点击列表页或者详情页面的【同意】，可以在“同意申请”页面确定是否继续审批，若继续审批则需要指定下一级审批人，若到此结束可以直接点击【同意】按钮表明审批通过。  \n如果拒绝车务申请，则点击【拒绝】，可以在“拒绝申请”页面备注拒绝说明，点击【提交】则表明拒绝审批。  \n![](http://10.15.1.75:18099/files/images/Userguide/d4a1b7c82e09f36a5d8b4c7f1a2e3d0c9876543210fedcba9876543210abcdef.jpg)\n![](http://10.15.1.75:18099/files/images/Userguide/e6f041c1be0f6048fd5323f767c3ea88cca1bd84dd1f5354cdc691f1938053d9.jpg)  \n同意车务申请  \n![](http://10.15.1.75:18099/files/images/Userguide/523eeef45bd950f5cf77c9d1f87049fbc7e776f3ccda44208be5d349e9ed0752.jpg)  \n#### 2.2.3.3. 实际金额确定  \n（1）web 端  \n申请人或者已审批的管理员可以在web 端-【车务管理】-【日常车务申请】-【我的车务申请】列表页，点击【实际金额】，在“实际金额确认”页面，填写实际车务产生的金额和对应附件，点击【确定】后，车务流程完成。  \n![](http://10.15.1.75:18099/files/images/Userguide/3572433f788413ed9e74e66a2b3daae42869e63f44cccab62f8b83dad3b8789c.jpg)  \n# （2）APP 端  \n申请的司机APP 端-【车务申请】，或者已审批的管理员可以在APP 端-【车务管理】列表页或者详情页，点击【确认金额】，在“实际金额确认”页面，填写实际车务产生的金额和对应附件，点击【实际金额确认】后，车务流程完成。  \n![](http://10.15.1.75:18099/files/images/Userguide/2461433f788413ed9e74e66a2b3daae42869e63f44cccab62f8b83dad3b8789c.jpg)\n![](http://10.15.1.75:18099/files/images/Userguide/1250433f788413ed9e74e66a2b3daae42869e63f44cccab62f8b83dad3b8789c.jpg)", "error": "You don't own the document c94de78657d611f095c20242ac1e0806.", "important_keywords": null, "questions": null, "timestamp": "2025-07-03T16:28:45.071865"}], "retry_info": {"can_retry": true, "retry_command": "python ragflow_chunker.py --retry-file failed_chunks/failed_chunks_30082cec57e711f091e50242ac1e0806_c94de78657d611f095c20242ac1e0806_20250703_162845.json"}}